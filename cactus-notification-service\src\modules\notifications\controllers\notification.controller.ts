import { FastifyRequest, FastifyReply } from 'fastify';
import { NotificationService } from '../services/notification.service';
import { NotificationListenerService } from '../services/notification-listener.service';
import { logger } from '../../../shared/logger/logger';
import { 
  NotificationFilters, 
  UserNotificationSettingsDto 
} from '../../../shared/types/notification.types';

export class NotificationController {
  private notificationService: NotificationService;
  private listenerService: NotificationListenerService;

  constructor(listenerService: NotificationListenerService) {
    this.notificationService = new NotificationService();
    this.listenerService = listenerService;
  }

  /**
   * Endpoint SSE para receber notificações em tempo real
   */
  async streamNotifications(request: FastifyRequest, reply: FastifyReply) {
    try {
      // TODO: Implementar autenticação e extrair userId do token
      const userId = request.headers['x-user-id'] as string || 'default-user';
      
      if (!userId) {
        return reply.status(401).send({ error: 'User ID required' });
      }

      logger.info(`SSE connection requested for user: ${userId}`);

      // Adicionar conexão SSE
      const connectionId = this.listenerService.addSSEConnection(userId, reply);

      // Enviar notificações não lidas existentes
      const unreadNotifications = await this.notificationService.findNotifications({
        userId,
        isRead: false,
        limit: 10,
      });

      for (const notification of unreadNotifications) {
        const sseData = `id: ${notification.id}\nevent: notification\ndata: ${JSON.stringify({
          notification,
          timestamp: new Date().toISOString(),
        })}\n\n`;
        
        reply.raw.write(sseData);
      }

      // A conexão permanece aberta até ser fechada pelo cliente
      
    } catch (error) {
      logger.error('Error in SSE endpoint:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Buscar notificações do usuário
   */
  async getNotifications(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = request.headers['x-user-id'] as string || 'default-user';
      const query = request.query as any;

      const filters: NotificationFilters = {
        userId,
        type: query.type,
        isRead: query.isRead !== undefined ? query.isRead === 'true' : undefined,
        limit: query.limit ? parseInt(query.limit) : 20,
        offset: query.offset ? parseInt(query.offset) : 0,
      };

      if (query.startDate) {
        filters.startDate = new Date(query.startDate);
      }
      if (query.endDate) {
        filters.endDate = new Date(query.endDate);
      }

      const notifications = await this.notificationService.findNotifications(filters);
      const unreadCount = await this.notificationService.getUnreadCount(userId);

      reply.send({
        notifications,
        unreadCount,
        pagination: {
          limit: filters.limit,
          offset: filters.offset,
          total: notifications.length,
        },
      });

    } catch (error) {
      logger.error('Error getting notifications:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Marcar notificação como lida
   */
  async markAsRead(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = request.headers['x-user-id'] as string || 'default-user';
      const { notificationId } = request.params as { notificationId: string };

      const notification = await this.notificationService.markAsRead(notificationId, userId);

      reply.send({
        success: true,
        notification,
      });

    } catch (error) {
      logger.error('Error marking notification as read:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Marcar múltiplas notificações como lidas
   */
  async markMultipleAsRead(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = request.headers['x-user-id'] as string || 'default-user';
      const { notificationIds } = request.body as { notificationIds: string[] };

      if (!Array.isArray(notificationIds)) {
        return reply.status(400).send({ error: 'notificationIds must be an array' });
      }

      const count = await this.notificationService.markMultipleAsRead(notificationIds, userId);

      reply.send({
        success: true,
        markedCount: count,
      });

    } catch (error) {
      logger.error('Error marking multiple notifications as read:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Obter configurações de notificação do usuário
   */
  async getUserSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = request.headers['x-user-id'] as string || 'default-user';

      const settings = await this.notificationService.getUserSettings(userId);

      reply.send({
        settings,
      });

    } catch (error) {
      logger.error('Error getting user settings:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Atualizar configurações de notificação do usuário
   */
  async updateUserSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = request.headers['x-user-id'] as string || 'default-user';
      const settingsData = request.body as Omit<UserNotificationSettingsDto, 'userId'>;

      const settings = await this.notificationService.updateUserSettings({
        userId,
        ...settingsData,
      });

      reply.send({
        success: true,
        settings,
      });

    } catch (error) {
      logger.error('Error updating user settings:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Obter contagem de notificações não lidas
   */
  async getUnreadCount(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = request.headers['x-user-id'] as string || 'default-user';

      const count = await this.notificationService.getUnreadCount(userId);

      reply.send({
        unreadCount: count,
      });

    } catch (error) {
      logger.error('Error getting unread count:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Obter estatísticas das conexões SSE (admin)
   */
  async getConnectionStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = this.listenerService.getConnectionStats();

      reply.send({
        stats,
      });

    } catch (error) {
      logger.error('Error getting connection stats:', error);
      reply.status(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Endpoint de health check
   */
  async healthCheck(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = this.listenerService.getConnectionStats();
      
      reply.send({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        connections: stats.totalConnections,
        service: 'cactus-notification-service',
      });

    } catch (error) {
      logger.error('Error in health check:', error);
      reply.status(500).send({ 
        status: 'unhealthy',
        error: 'Internal server error',
      });
    }
  }
}
