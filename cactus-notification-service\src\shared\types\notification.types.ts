import { NotificationType } from '@prisma/client';

export interface NotificationData {
  contractId?: string;
  entityName?: string;
  entityUuid?: string;
  contractType?: string;
  expirationDate?: string;
  daysUntilExpiration?: number;
  [key: string]: any;
}

export interface CreateNotificationDto {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: NotificationData;
}

export interface UpdateNotificationDto {
  isRead?: boolean;
  readAt?: Date;
}

export interface NotificationFilters {
  userId?: string;
  type?: NotificationType;
  isRead?: boolean;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export interface UserNotificationSettingsDto {
  userId: string;
  contractExpiration?: boolean;
  documentExpiration?: boolean;
  emailNotifications?: boolean;
  pushNotifications?: boolean;
}

export interface ContractExpirationInfo {
  contractId: string;
  entityType: string;
  entityUuid: string;
  entityName?: string;
  contractType: string;
  expirationDate: Date;
  daysUntilExpiration: number;
  currentVersion: number;
  status: string;
}

export interface SSENotificationEvent {
  id: string;
  event: 'notification' | 'heartbeat';
  data: {
    notification?: any;
    timestamp: string;
  };
}
