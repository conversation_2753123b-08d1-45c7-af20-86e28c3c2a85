import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { GetBankDataQueryDto } from './get-bank-data-query.dto';
import {
  EntityType,
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

describe('GetBankDataQueryDto', () => {
  describe('Valid inputs', () => {
    it('should validate with all valid parameters', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        page: '1',
        limit: '10',
        employeeId: '123',
        status: BankDataStatus.ACTIVE,
        entityType: EntityType.COLLABORATE,
        bankName: 'Banco do Brasil',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: '<PERSON>',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: 'false',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal parameters', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.page).toBe(1);
      expect(dto.limit).toBe(10);
    });

    it('should validate with only pagination parameters', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        page: '2',
        limit: '20',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.page).toBe(2);
      expect(dto.limit).toBe(20);
    });

    it('should transform string boolean to boolean for isDigitalBank', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        isDigitalBank: 'true',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.isDigitalBank).toBe(true);
    });

    it('should transform string "false" to boolean false for isDigitalBank', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        isDigitalBank: 'false',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.isDigitalBank).toBe(false);
    });
  });

  describe('Invalid inputs', () => {
    it('should fail validation for invalid page number', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        page: '0',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const pageError = errors.find((error) => error.property === 'page');
      expect(pageError).toBeDefined();
      expect(Object.values(pageError?.constraints || {})).toContain(
        'Page must be at least 1',
      );
    });

    it('should fail validation for limit exceeding maximum', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        limit: '101',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const limitError = errors.find((error) => error.property === 'limit');
      expect(limitError).toBeDefined();
      expect(Object.values(limitError?.constraints || {})).toContain(
        'Limit must be at most 100',
      );
    });

    it('should fail validation for invalid employeeId string', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        employeeId: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const employeeIdError = errors.find(
        (error) => error.property === 'employeeId',
      );
      expect(employeeIdError).toBeDefined();
    });

    it('should fail validation for invalid entity type', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        entityType: 'INVALID_TYPE',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const entityTypeError = errors.find(
        (error) => error.property === 'entityType',
      );
      expect(entityTypeError).toBeDefined();
      expect(Object.values(entityTypeError?.constraints || {})).toContain(
        'Entity type must be a valid enum value',
      );
    });

    it('should fail validation for invalid account type', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        accountType: 'INVALID_ACCOUNT_TYPE',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const accountTypeError = errors.find(
        (error) => error.property === 'accountType',
      );
      expect(accountTypeError).toBeDefined();
      expect(Object.values(accountTypeError?.constraints || {})).toContain(
        'Account type must be a valid enum value',
      );
    });

    it('should fail validation for invalid PIX key type', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        pixKeyType: 'INVALID_PIX_TYPE',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const pixKeyTypeError = errors.find(
        (error) => error.property === 'pixKeyType',
      );
      expect(pixKeyTypeError).toBeDefined();
      expect(Object.values(pixKeyTypeError?.constraints || {})).toContain(
        'PIX key type must be a valid enum value',
      );
    });

    it('should fail validation for invalid status', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        status: 'INVALID_STATUS',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const statusError = errors.find((error) => error.property === 'status');
      expect(statusError).toBeDefined();
      expect(Object.values(statusError?.constraints || {})).toContain(
        'Status must be a valid enum value',
      );
    });

    it('should fail validation for bank name exceeding max length', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        bankName: 'a'.repeat(101), // Exceeds 100 character limit
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankNameError = errors.find(
        (error) => error.property === 'bankName',
      );
      expect(bankNameError).toBeDefined();
      expect(Object.values(bankNameError?.constraints || {})).toContain(
        'Bank name must be at most 100 characters long',
      );
    });

    it('should fail validation for bank code exceeding max length', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        bankCode: 'a'.repeat(11), // Exceeds 10 character limit
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankCodeError = errors.find(
        (error) => error.property === 'bankCode',
      );
      expect(bankCodeError).toBeDefined();
      expect(Object.values(bankCodeError?.constraints || {})).toContain(
        'Bank code must be at most 10 characters long',
      );
    });

    it('should fail validation for invalid date string', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        createdAt: 'invalid-date',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const createdAtError = errors.find(
        (error) => error.property === 'createdAt',
      );
      expect(createdAtError).toBeDefined();
      expect(Object.values(createdAtError?.constraints || {})).toContain(
        'Created at must be a valid date string',
      );
    });

    it('should fail validation for non-integer employeeId', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        employeeId: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const employeeIdError = errors.find(
        (error) => error.property === 'employeeId',
      );
      expect(employeeIdError).toBeDefined();
    });
  });

  describe('Transformations', () => {
    it('should transform string numbers to integers', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        page: '5',
        limit: '25',
        offset: '50',
        employeeId: '999',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.page).toBe(5);
      expect(dto.limit).toBe(25);
      expect(dto.employeeId).toBe(999);
    });

    it('should handle boolean transformation correctly', async () => {
      const dtoTrue = plainToClass(GetBankDataQueryDto, {
        isDigitalBank: 'true',
      });

      const dtoFalse = plainToClass(GetBankDataQueryDto, {
        isDigitalBank: 'false',
      });

      const errorsTrue = await validate(dtoTrue);
      const errorsFalse = await validate(dtoFalse);

      expect(errorsTrue).toHaveLength(0);
      expect(errorsFalse).toHaveLength(0);
      expect(dtoTrue.isDigitalBank).toBe(true);
      expect(dtoFalse.isDigitalBank).toBe(false);
    });
  });
});
