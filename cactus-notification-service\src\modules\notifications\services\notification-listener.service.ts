import { Client } from 'pg';
import { EventEmitter } from 'events';
import { logger } from '../../../shared/logger/logger';
import { NotificationService } from './notification.service';
import { SSENotificationEvent } from '../../../shared/types/notification.types';

export interface SSEConnection {
  userId: string;
  connectionId: string;
  reply: any; // Fastify reply object
  lastHeartbeat: Date;
}

export class NotificationListenerService extends EventEmitter {
  private pgClient: Client;
  private sseConnections: Map<string, SSEConnection> = new Map();
  private notificationService: NotificationService;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isListening = false;

  constructor() {
    super();
    this.notificationService = new NotificationService();
    
    // Configurar cliente PostgreSQL para LISTEN/NOTIFY
    this.pgClient = new Client({
      connectionString: process.env.DATABASE_URL,
    });
  }

  /**
   * Iniciar o listener de notificações
   */
  async startListener(): Promise<void> {
    try {
      if (this.isListening) {
        logger.warn('Notification listener is already running');
        return;
      }

      // Conectar ao PostgreSQL
      await this.pgClient.connect();
      logger.info('Connected to PostgreSQL for LISTEN/NOTIFY');

      // Configurar LISTEN
      await this.pgClient.query('LISTEN new_notification');
      logger.info('PostgreSQL LISTEN configured for new_notification channel');

      // Configurar handler para notificações
      this.pgClient.on('notification', this.handleNotification.bind(this));

      // Configurar handler para erros
      this.pgClient.on('error', (err) => {
        logger.error('PostgreSQL client error:', err);
        this.reconnect();
      });

      // Iniciar heartbeat para conexões SSE
      this.startHeartbeat();

      this.isListening = true;
      logger.info('Notification listener started successfully');

    } catch (error) {
      logger.error('Failed to start notification listener:', error);
      throw error;
    }
  }

  /**
   * Parar o listener de notificações
   */
  async stopListener(): Promise<void> {
    try {
      this.isListening = false;

      // Parar heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }

      // Fechar todas as conexões SSE
      this.closeAllSSEConnections();

      // Desconectar do PostgreSQL
      if (this.pgClient) {
        await this.pgClient.end();
        logger.info('Disconnected from PostgreSQL');
      }

      logger.info('Notification listener stopped');

    } catch (error) {
      logger.error('Error stopping notification listener:', error);
    }
  }

  /**
   * Adicionar conexão SSE
   */
  addSSEConnection(userId: string, reply: any): string {
    const connectionId = `${userId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const connection: SSEConnection = {
      userId,
      connectionId,
      reply,
      lastHeartbeat: new Date(),
    };

    this.sseConnections.set(connectionId, connection);
    
    logger.info(`SSE connection added`, { userId, connectionId, totalConnections: this.sseConnections.size });

    // Configurar headers SSE
    reply.raw.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // Enviar evento de conexão estabelecida
    this.sendSSEEvent(connectionId, {
      id: connectionId,
      event: 'heartbeat',
      data: {
        timestamp: new Date().toISOString(),
        message: 'Connection established',
      },
    });

    // Configurar cleanup quando a conexão for fechada
    reply.raw.on('close', () => {
      this.removeSSEConnection(connectionId);
    });

    reply.raw.on('error', (err: Error) => {
      logger.error(`SSE connection error for ${connectionId}:`, err);
      this.removeSSEConnection(connectionId);
    });

    return connectionId;
  }

  /**
   * Remover conexão SSE
   */
  removeSSEConnection(connectionId: string): void {
    const connection = this.sseConnections.get(connectionId);
    if (connection) {
      this.sseConnections.delete(connectionId);
      logger.info(`SSE connection removed`, { 
        userId: connection.userId, 
        connectionId, 
        totalConnections: this.sseConnections.size 
      });
    }
  }

  /**
   * Obter estatísticas das conexões
   */
  getConnectionStats(): { totalConnections: number; userConnections: Record<string, number> } {
    const userConnections: Record<string, number> = {};
    
    for (const connection of this.sseConnections.values()) {
      userConnections[connection.userId] = (userConnections[connection.userId] || 0) + 1;
    }

    return {
      totalConnections: this.sseConnections.size,
      userConnections,
    };
  }

  /**
   * Handler para notificações do PostgreSQL
   */
  private async handleNotification(msg: any): Promise<void> {
    try {
      logger.info('Received PostgreSQL notification:', msg);

      const notificationId = msg.payload;
      if (!notificationId) {
        logger.warn('Received notification without payload');
        return;
      }

      // Buscar a notificação criada
      const notifications = await this.notificationService.findNotifications({
        limit: 1,
        offset: 0,
      });

      const notification = notifications.find(n => n.id === notificationId);
      if (!notification) {
        logger.warn(`Notification not found: ${notificationId}`);
        return;
      }

      // Enviar para conexões SSE do usuário
      await this.broadcastNotificationToUser(notification.userId, notification);

      // Emitir evento interno
      this.emit('notification', notification);

    } catch (error) {
      logger.error('Error handling PostgreSQL notification:', error);
    }
  }

  /**
   * Enviar notificação para todas as conexões SSE de um usuário
   */
  private async broadcastNotificationToUser(userId: string, notification: any): Promise<void> {
    const userConnections = Array.from(this.sseConnections.values())
      .filter(conn => conn.userId === userId);

    if (userConnections.length === 0) {
      logger.debug(`No SSE connections found for user ${userId}`);
      return;
    }

    const event: SSENotificationEvent = {
      id: notification.id,
      event: 'notification',
      data: {
        notification,
        timestamp: new Date().toISOString(),
      },
    };

    for (const connection of userConnections) {
      try {
        this.sendSSEEvent(connection.connectionId, event);
      } catch (error) {
        logger.error(`Failed to send notification to connection ${connection.connectionId}:`, error);
        this.removeSSEConnection(connection.connectionId);
      }
    }

    logger.info(`Notification sent to ${userConnections.length} SSE connections for user ${userId}`);
  }

  /**
   * Enviar evento SSE para uma conexão específica
   */
  private sendSSEEvent(connectionId: string, event: SSENotificationEvent): void {
    const connection = this.sseConnections.get(connectionId);
    if (!connection) {
      logger.warn(`SSE connection not found: ${connectionId}`);
      return;
    }

    try {
      const sseData = `id: ${event.id}\nevent: ${event.event}\ndata: ${JSON.stringify(event.data)}\n\n`;
      connection.reply.raw.write(sseData);
      connection.lastHeartbeat = new Date();
    } catch (error) {
      logger.error(`Failed to write to SSE connection ${connectionId}:`, error);
      this.removeSSEConnection(connectionId);
    }
  }

  /**
   * Iniciar heartbeat para manter conexões SSE vivas
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = new Date();
      const staleConnections: string[] = [];

      for (const [connectionId, connection] of this.sseConnections.entries()) {
        const timeSinceLastHeartbeat = now.getTime() - connection.lastHeartbeat.getTime();
        
        // Remover conexões inativas há mais de 5 minutos
        if (timeSinceLastHeartbeat > 5 * 60 * 1000) {
          staleConnections.push(connectionId);
        } else {
          // Enviar heartbeat
          try {
            this.sendSSEEvent(connectionId, {
              id: `heartbeat-${Date.now()}`,
              event: 'heartbeat',
              data: {
                timestamp: now.toISOString(),
              },
            });
          } catch (error) {
            staleConnections.push(connectionId);
          }
        }
      }

      // Remover conexões inativas
      for (const connectionId of staleConnections) {
        this.removeSSEConnection(connectionId);
      }

    }, 30000); // Heartbeat a cada 30 segundos
  }

  /**
   * Fechar todas as conexões SSE
   */
  private closeAllSSEConnections(): void {
    for (const [connectionId, connection] of this.sseConnections.entries()) {
      try {
        connection.reply.raw.end();
      } catch (error) {
        logger.error(`Error closing SSE connection ${connectionId}:`, error);
      }
    }
    this.sseConnections.clear();
    logger.info('All SSE connections closed');
  }

  /**
   * Reconectar ao PostgreSQL em caso de erro
   */
  private async reconnect(): Promise<void> {
    try {
      logger.info('Attempting to reconnect to PostgreSQL...');
      
      await this.pgClient.end();
      this.pgClient = new Client({
        connectionString: process.env.DATABASE_URL,
      });
      
      await this.startListener();
      
    } catch (error) {
      logger.error('Failed to reconnect to PostgreSQL:', error);
      // Tentar novamente em 5 segundos
      setTimeout(() => this.reconnect(), 5000);
    }
  }
}
