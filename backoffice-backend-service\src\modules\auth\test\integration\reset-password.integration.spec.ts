import { Test } from '@nestjs/testing';
import { Auth<PERSON>ontroller } from '../../auth.controller';
import { AuthService } from '../../auth.service';
import { JwtService } from '@nestjs/jwt';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { UsersService } from '../../../users/users.service';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../../infrastructure/utils/request.utils.service';
import {
  mockConfigService,
  mockRequestUtilsService,
  createTestUser,
  resetAllMocks,
  TestUser,
} from './auth-test.config';
import { ForgotPasswordDto } from '../../dto/forgot-password.dto';
import { ResetPasswordDto } from '../../dto/reset-password.dto';
import { LoginDto } from '../../dto/login.dto';
import { EventsTestModule } from '../../../../infrastructure/events/test/events-test.module';
import { EmployeeService } from '../../../finance/employee/employee.service';
import { CustomerRepository } from '../../../customers/infrastructure/repositories/customer.repository';
import { SupplierAuthService } from '../../supplier-auth/supplier-auth.service';
import { PrismaSupplierRepository } from '../../../../infrastructure/repositories/prisma-supplier.repository';
import { PrismaEmployeeRepository } from '../../../../infrastructure/repositories/prisma-employee.repository';

interface KeycloakTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

describe('Auth Reset Password Integration Tests', () => {
  let controller: AuthController;
  let _authService: AuthService;
  let testUser: TestUser;

  const mockUserRepository = {
    findByEmail: jest.fn(),
    create: jest.fn(),
    updateUserKeycloakId: jest.fn(),
    findById: jest.fn(),
    update: jest.fn(),
  };

  const mockPasswordResetTokenRepository = {
    create: jest.fn(),
    findByToken: jest.fn(),
    markAsUsed: jest.fn(),
    deleteExpiredTokens: jest.fn(),
    deleteByToken: jest.fn(),
    deleteByUserId: jest.fn(),
  };

  const mockEmailService = {
    sendPasswordResetEmail: jest.fn(),
  };

  const mockUsersService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockImplementation(() => 'mock-jwt-token'),
    decode: jest.fn(),
  };

  const mockKeycloakService = {
    token: jest.fn().mockResolvedValue({
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token',
      expires_in: 300,
    } as KeycloakTokenResponse),
    refreshToken: jest.fn(),
    validateToken: jest.fn(),
    getUserInfo: jest.fn(),
    logout: jest.fn(),
    resetPassword: jest.fn(),
  };

  const mockKeycloakIdentityProvider = {
    registerUser: jest.fn(),
    assignUserRoles: jest.fn(),
    authenticate: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    getUserInfo: jest.fn(),
    keycloakAdminUtils: {
      getAdminAuthHeaders: jest.fn().mockResolvedValue({
        Authorization: 'Bearer mock-token',
        'Content-Type': 'application/json',
      }),
    },
  };

  const mockEventPublisher = {
    publish: jest.fn(),
  };

  const mockSupplierAuthService = {
    // Add any methods that might be called
  };

  const mockPrismaSupplierRepository = {
    // Add any methods that might be called
  };

  const mockPrismaEmployeeRepository = {
    // Add any methods that might be called
  };

  const mockUsersOtpRepository = {
    create: jest.fn(),
    findByEmail: jest.fn(),
    markAsUsed: jest.fn(),
    deleteExpiredTokens: jest.fn(),
  };

  beforeEach(async () => {
    resetAllMocks();
    testUser = createTestUser();

    const moduleRef = await Test.createTestingModule({
      imports: [EventsTestModule],
      controllers: [AuthController],
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: mockPasswordResetTokenRepository,
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: KeycloakService,
          useValue: mockKeycloakService,
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakIdentityProvider,
        },
        {
          provide: EventPublisherService,
          useValue: mockEventPublisher,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RequestUtilsService,
          useValue: mockRequestUtilsService,
        },
        {
          provide: EmployeeService,
          useValue: {},
        },
        {
          provide: CustomerRepository,
          useValue: {},
        },
        {
          provide: SupplierAuthService,
          useValue: mockSupplierAuthService,
        },
        {
          provide: PrismaSupplierRepository,
          useValue: mockPrismaSupplierRepository,
        },
        {
          provide: PrismaEmployeeRepository,
          useValue: mockPrismaEmployeeRepository,
        },
        {
          provide: 'UsersOtpRepository',
          useValue: mockUsersOtpRepository,
        },
      ],
    }).compile();

    controller = moduleRef.get<AuthController>(AuthController);
    _authService = moduleRef.get<AuthService>(AuthService);
  });

  describe('Password Reset Flow', () => {
    it('should complete the full password reset flow', async () => {
      // 1. Setup test user
      const user: TestUser = {
        id: 'user-123',
        email: testUser.email,
        keycloakId: 'keycloak-123',
        name: 'Usuário Teste',
        password: 'Senha123!',
        accessToken: null,
        refreshToken: null,
      };
      mockUserRepository.findByEmail.mockResolvedValue(user);
      mockUserRepository.findById.mockResolvedValue(user);

      // 2. Request password reset
      const forgotPasswordDto: ForgotPasswordDto = { email: testUser.email };
      await controller.forgotPassword(forgotPasswordDto);

      // 3. Verify token was created
      expect(mockPasswordResetTokenRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: user.id,
          token: expect.any(String),
          used: false,
        }),
      );

      // 4. Setup token mock for reset
      const resetToken = {
        id: 'token-123',
        userId: user.id,
        token: '123456',
        expiresAt: new Date(Date.now() + 3600000), // Valid for 1 hour
        used: false,
      };
      mockPasswordResetTokenRepository.findByToken.mockResolvedValue(
        resetToken,
      );

      // 5. Reset password
      const resetPasswordDto: ResetPasswordDto = {
        token: '123456',
        newPassword: 'NewPassword123!',
      };
      await controller.resetPassword(resetPasswordDto);

      // 6. Verify Keycloak password reset was called with plain text password
      expect(mockKeycloakService.resetPassword).toHaveBeenCalledWith(
        user.keycloakId,
        resetPasswordDto.newPassword,
      );

      // 7. Verify local database update was called with hashed password
      expect(mockUserRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({
          id: user.id,
          password: expect.stringMatching(/^\$2[aby]\$\d+\$/) as unknown, // bcrypt hash pattern
        }),
      );

      // 8. Verify token was marked as used
      expect(mockPasswordResetTokenRepository.markAsUsed).toHaveBeenCalledWith(
        resetPasswordDto.token,
      );

      // 9. Verify can login with new password
      const loginDto: LoginDto = {
        username: testUser.email,
        password: resetPasswordDto.newPassword,
      };

      mockKeycloakService.token.mockResolvedValueOnce({
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        expires_in: 300,
      } as KeycloakTokenResponse);

      interface LoginResponseBody {
        access_token?: string;
      }
      const loginResult = (await controller.token(
        loginDto,
      )) as LoginResponseBody;
      expect(loginResult.access_token).toBeDefined();
    });

    it('should return 401 for invalid token', async () => {
      mockPasswordResetTokenRepository.findByToken.mockResolvedValue(null);

      const resetPasswordDto: ResetPasswordDto = {
        token: 'invalid-token',
        newPassword: 'NewPassword123!',
      };

      await expect(
        controller.resetPassword(resetPasswordDto),
      ).rejects.toMatchObject({
        status: 401,
        message: 'Token inválido ou expirado',
      });
    });

    it('should return 401 for expired token', async () => {
      const user = {
        id: 'user-123',
        email: testUser.email,
        keycloakId: 'keycloak-123',
      };
      mockUserRepository.findById.mockResolvedValue(user);

      const expiredToken = {
        id: 'token-123',
        userId: user.id,
        token: '123456',
        expiresAt: new Date(Date.now() - 1000), // Expired
        used: false,
      };
      mockPasswordResetTokenRepository.findByToken.mockResolvedValue(
        expiredToken,
      );

      const resetPasswordDto: ResetPasswordDto = {
        token: '123456',
        newPassword: 'NewPassword123!',
      };

      await expect(
        controller.resetPassword(resetPasswordDto),
      ).rejects.toMatchObject({
        status: 401,
        message: 'Token inválido ou expirado',
      });
    });

    it('should return 401 for used token', async () => {
      const user = {
        id: 'user-123',
        email: testUser.email,
        keycloakId: 'keycloak-123',
      };
      mockUserRepository.findById.mockResolvedValue(user);

      const usedToken = {
        id: 'token-123',
        userId: user.id,
        token: '123456',
        expiresAt: new Date(Date.now() + 3600000), // Valid
        used: true,
      };
      mockPasswordResetTokenRepository.findByToken.mockResolvedValue(usedToken);

      const resetPasswordDto: ResetPasswordDto = {
        token: '123456',
        newPassword: 'NewPassword123!',
      };

      await expect(
        controller.resetPassword(resetPasswordDto),
      ).rejects.toMatchObject({
        status: 401,
        message: 'Token inválido ou expirado',
      });
    });
  });
});
