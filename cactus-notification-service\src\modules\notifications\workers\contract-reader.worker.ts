import { Worker, Queue, Job } from 'bullmq';
import Redis from 'ioredis';
import { ContractReaderService } from '../services/contract-reader.service';
import { logger } from '../../../shared/logger/logger';
import { connectDatabases } from '../../../shared/database/prisma';
import { ExpiringContract } from '../../../shared/types/contract.types';

// Configuração do Redis
const redisConnection = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  maxRetriesPerRequest: 3,
});

// Queue para enviar contratos encontrados para processamento
const notificationQueue = new Queue('notification-processing', {
  connection: redisConnection,
});

// Queue para o próprio worker de leitura de contratos
const contractReaderQueue = new Queue('contract-reader', {
  connection: redisConnection,
});

const contractReaderService = new ContractReaderService();

// Dados do job de leitura de contratos
interface ContractReaderJobData {
  type: 'check-expiring' | 'check-expired';
  daysAhead?: number[];
}

// Worker para processar jobs de leitura de contratos
const contractReaderWorker = new Worker(
  'contract-reader',
  async (job: Job<ContractReaderJobData>) => {
    logger.info(`Processing contract reader job: ${job.name}`, { jobId: job.id, data: job.data });

    try {
      // Testar conexão com backoffice
      const isConnected = await contractReaderService.testConnection();
      if (!isConnected) {
        throw new Error('Cannot connect to backoffice database');
      }

      let contracts: ExpiringContract[] = [];

      if (job.data.type === 'check-expiring') {
        const daysAhead = job.data.daysAhead || [30, 15, 7, 1];
        contracts = await contractReaderService.findExpiringContracts(daysAhead);
      } else if (job.data.type === 'check-expired') {
        contracts = await contractReaderService.findExpiredContracts();
      }

      logger.info(`Found ${contracts.length} contracts to process`);

      // Enviar cada contrato para a queue de processamento de notificações
      for (const contract of contracts) {
        await notificationQueue.add(
          'process-contract-notification',
          {
            contract,
            notificationType: job.data.type === 'check-expired' ? 'CONTRACT_EXPIRED' : 'CONTRACT_EXPIRING',
          },
          {
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          }
        );
      }

      return {
        success: true,
        contractsFound: contracts.length,
        type: job.data.type,
      };

    } catch (error) {
      logger.error('Error in contract reader worker:', error);
      throw error;
    }
  },
  {
    connection: redisConnection,
    concurrency: 1, // Processar um job por vez
  }
);

// Event listeners
contractReaderWorker.on('completed', (job) => {
  logger.info(`Contract reader job completed: ${job.id}`, job.returnvalue);
});

contractReaderWorker.on('failed', (job, err) => {
  logger.error(`Contract reader job failed: ${job?.id}`, err);
});

contractReaderWorker.on('error', (err) => {
  logger.error('Contract reader worker error:', err);
});

// Função para configurar jobs recorrentes
async function setupRecurringJobs() {
  try {
    // Limpar jobs recorrentes existentes
    await contractReaderQueue.obliterate({ force: true });

    // Job para verificar contratos vencendo (a cada hora)
    const cronExpression = process.env.CONTRACT_CHECK_CRON || '0 * * * *';
    const daysAhead = process.env.CONTRACT_EXPIRATION_DAYS_AHEAD?.split(',').map(d => parseInt(d.trim())) || [30, 15, 7, 1];

    await contractReaderQueue.add(
      'check-expiring-contracts',
      {
        type: 'check-expiring',
        daysAhead,
      },
      {
        repeat: {
          pattern: cronExpression,
        },
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      }
    );

    // Job para verificar contratos expirados (diariamente às 9h)
    await contractReaderQueue.add(
      'check-expired-contracts',
      {
        type: 'check-expired',
      },
      {
        repeat: {
          pattern: '0 9 * * *', // Todos os dias às 9h
        },
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      }
    );

    logger.info('Recurring contract reader jobs configured successfully');
    logger.info(`Expiring contracts check: ${cronExpression}`);
    logger.info(`Expired contracts check: 0 9 * * *`);
    logger.info(`Days ahead for expiring contracts: ${daysAhead.join(', ')}`);

  } catch (error) {
    logger.error('Error setting up recurring jobs:', error);
    throw error;
  }
}

// Função principal para iniciar o worker
async function startContractReaderWorker() {
  try {
    logger.info('Starting Contract Reader Worker...');

    // Conectar às databases
    await connectDatabases();

    // Configurar jobs recorrentes
    await setupRecurringJobs();

    logger.info('Contract Reader Worker started successfully');

    // Manter o processo vivo
    process.on('SIGINT', async () => {
      logger.info('Shutting down Contract Reader Worker...');
      await contractReaderWorker.close();
      await redisConnection.quit();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('Shutting down Contract Reader Worker...');
      await contractReaderWorker.close();
      await redisConnection.quit();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start Contract Reader Worker:', error);
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  startContractReaderWorker();
}

export { contractReaderWorker, contractReaderQueue, notificationQueue, startContractReaderWorker };
