import { Contract } from "@modules/documents/domain/entities/contract.entity";

export interface IContractRepository {
  create(contract: Partial<Contract>): Promise<Contract>;
  findByUuid(uuid: string): Promise<Contract | null>;
  list(
    filters: Partial<Pick<Contract, 'entityType' | 'entityUuid' | 'status'>>,
    limit: number,
    offset: number,
  ): Promise<{ items: Contract[]; total: number }>;
  updateStatusToArchived(
    uuid: string,
    archivedBy: string,
    updatedAt: Date,
  ): Promise<void>;
  delete(uuid: string): Promise<void>;
  update(uuid: string, data: Partial<Contract>): Promise<Contract>;
  createVersion(contractId: string, data: { uploadedBy: string; filePath: string; signed: boolean; startDate?: Date; expirationDate?: Date; observations?: string }): Promise<void>;
}
