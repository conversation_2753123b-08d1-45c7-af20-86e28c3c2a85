import { Test, TestingModule } from '@nestjs/testing';
import { ContactsService } from './contacts.service';
import { CreateCustomerContactUseCase } from '@/core/application/use-cases/customer/create-customer-contact.use-case';
import { FindCustomerByUuidUseCase } from '../application/use-cases/find-customer-by-uuid.use-case';
import { ListCustomerContactsUseCase } from '@/core/application/use-cases/customer/list-customer-contacts.use-case';
import { DeleteCustomerContactUseCase } from '@/core/application/use-cases/customer/delete-customer-contact.use-case';
import { UpdateCustomerContactUseCase } from '@/core/application/use-cases/customer/update-customer-contact.use-case';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';

describe('ContactsService', () => {
  let service: ContactsService;
  let findCustomerByUuidUseCase: jest.Mocked<FindCustomerByUuidUseCase>;
  let createCustomerContactUseCase: jest.Mocked<CreateCustomerContactUseCase>;
  let listCustomerContactsUseCase: jest.Mocked<ListCustomerContactsUseCase>;
  let deleteCustomerContactUseCase: jest.Mocked<DeleteCustomerContactUseCase>;
  let updateCustomerContactUseCase: jest.Mocked<UpdateCustomerContactUseCase>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContactsService,
        {
          provide: FindCustomerByUuidUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: CreateCustomerContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListCustomerContactsUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteCustomerContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: UpdateCustomerContactUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<ContactsService>(ContactsService);
    findCustomerByUuidUseCase = module.get(FindCustomerByUuidUseCase);
    createCustomerContactUseCase = module.get(CreateCustomerContactUseCase);
    listCustomerContactsUseCase = module.get(ListCustomerContactsUseCase);
    deleteCustomerContactUseCase = module.get(DeleteCustomerContactUseCase);
    updateCustomerContactUseCase = module.get(UpdateCustomerContactUseCase);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create contacts for a customer', async () => {
      const customerUuid = 'customer-uuid-123';
      const customerId = 1;
      const contactsData: CreateContactDto[] = [
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
        {
          contact: '+1234567890',
          type: 'phone',
          area: 'Support',
          responsible: 'Jane Smith',
        },
      ];

      const mockCustomer = {
        id: customerId,
        uuid: customerUuid,
        name: 'Test Customer',
      };

      const mockCreatedContacts = [
        {
          id: 1,
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
          customerId: customerId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 2,
          contact: '+1234567890',
          type: 'phone',
          area: 'Support',
          responsible: 'Jane Smith',
          customerId: customerId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      findCustomerByUuidUseCase.execute.mockResolvedValue(mockCustomer as any);
      createCustomerContactUseCase.execute.mockResolvedValue(mockCreatedContacts[0] as any);
      createCustomerContactUseCase.execute.mockResolvedValueOnce(mockCreatedContacts[0] as any);
      createCustomerContactUseCase.execute.mockResolvedValueOnce(mockCreatedContacts[1] as any);

      const result = await service.create(customerUuid, contactsData);

      expect(findCustomerByUuidUseCase.execute).toHaveBeenCalledWith(customerUuid);
      expect(createCustomerContactUseCase.execute).toHaveBeenCalledTimes(2);
      expect(result).toEqual([
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
        {
          contact: '+1234567890',
          type: 'phone',
          area: 'Support',
          responsible: 'Jane Smith',
        },
      ]);
    });

    it('should handle empty contacts array', async () => {
      const customerUuid = 'customer-uuid-123';
      const customerId = 1;
      const contactsData: CreateContactDto[] = [];

      const mockCustomer = {
        id: customerId,
        uuid: customerUuid,
        name: 'Test Customer',
      };

      findCustomerByUuidUseCase.execute.mockResolvedValue(mockCustomer as any);

      const result = await service.create(customerUuid, contactsData);

      expect(findCustomerByUuidUseCase.execute).toHaveBeenCalledWith(customerUuid);
      expect(createCustomerContactUseCase.execute).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });

  describe('findAll', () => {
    it('should return all contacts for a customer', async () => {
      const customerUuid = 'customer-uuid-123';
      const customerId = 1;

      const mockCustomer = {
        id: customerId,
        uuid: customerUuid,
        name: 'Test Customer',
      };

      const mockContacts = [
        {
          id: 1,
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
          customerId: customerId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 2,
          contact: '+1234567890',
          type: 'phone',
          area: 'Support',
          responsible: 'Jane Smith',
          customerId: customerId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      findCustomerByUuidUseCase.execute.mockResolvedValue(mockCustomer as any);
      listCustomerContactsUseCase.execute.mockResolvedValue(mockContacts as any);

      const result = await service.findAll(customerUuid);

      expect(findCustomerByUuidUseCase.execute).toHaveBeenCalledWith(customerUuid);
      expect(listCustomerContactsUseCase.execute).toHaveBeenCalledWith(customerId);
      expect(result).toEqual({
        contacts: [
          {
            id: 1,
            contact: '<EMAIL>',
            type: 'email',
            area: 'Sales',
            responsible: 'John Doe',
          },
          {
            id: 2,
            contact: '+1234567890',
            type: 'phone',
            area: 'Support',
            responsible: 'Jane Smith',
          },
        ],
      });
    });

    it('should return empty contacts array when no contacts found', async () => {
      const customerUuid = 'customer-uuid-123';
      const customerId = 1;

      const mockCustomer = {
        id: customerId,
        uuid: customerUuid,
        name: 'Test Customer',
      };

      findCustomerByUuidUseCase.execute.mockResolvedValue(mockCustomer as any);
      listCustomerContactsUseCase.execute.mockResolvedValue([]);

      const result = await service.findAll(customerUuid);

      expect(findCustomerByUuidUseCase.execute).toHaveBeenCalledWith(customerUuid);
      expect(listCustomerContactsUseCase.execute).toHaveBeenCalledWith(customerId);
      expect(result).toEqual({ contacts: [] });
    });
  });

  describe('findOne', () => {
    it('should return a contact by id', () => {
      const contactId = 1;
      const expectedResult = `This action returns a #${contactId} contact`;

      const result = service.findOne(contactId);

      expect(result).toBe(expectedResult);
    });
  });

  describe('update', () => {
    it('should update a contact', async () => {
      const contactId = 'contact-uuid-123';
      const updateContactDto: UpdateContactDto = {
        contact: '<EMAIL>',
        type: 'email',
        area: 'Marketing',
        responsible: 'Updated Person',
      };

      const mockUpdatedContact = {
        id: 1,
        contact: '<EMAIL>',
        type: 'email',
        area: 'Marketing',
        responsible: 'Updated Person',
        customerId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      updateCustomerContactUseCase.execute.mockResolvedValue(mockUpdatedContact as any);

      const result = await service.update(contactId, updateContactDto);

      expect(updateCustomerContactUseCase.execute).toHaveBeenCalledWith(contactId, updateContactDto);
      expect(result).toEqual(mockUpdatedContact);
    });

    it('should handle partial update', async () => {
      const contactId = 'contact-uuid-123';
      const updateContactDto: UpdateContactDto = {
        contact: '<EMAIL>',
      };

      const mockUpdatedContact = {
        id: 1,
        contact: '<EMAIL>',
        type: 'email',
        area: 'Sales',
        responsible: 'John Doe',
        customerId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      updateCustomerContactUseCase.execute.mockResolvedValue(mockUpdatedContact as any);

      const result = await service.update(contactId, updateContactDto);

      expect(updateCustomerContactUseCase.execute).toHaveBeenCalledWith(contactId, updateContactDto);
      expect(result).toEqual(mockUpdatedContact);
    });
  });

  describe('remove', () => {
    it('should delete a contact', async () => {
      const contactId = 'contact-uuid-123';

      const mockDeletedContact = {
        id: 1,
        contact: '<EMAIL>',
        type: 'email',
        area: 'Sales',
        responsible: 'John Doe',
        customerId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      deleteCustomerContactUseCase.execute.mockResolvedValue(mockDeletedContact as any);

      const result = await service.remove(contactId);

      expect(deleteCustomerContactUseCase.execute).toHaveBeenCalledWith(contactId);
      expect(result).toEqual(mockDeletedContact);
    });
  });
});
