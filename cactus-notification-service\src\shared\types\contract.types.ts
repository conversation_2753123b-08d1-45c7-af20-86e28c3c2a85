export interface BackofficeContractQuery {
  entityType?: string;
  contractType?: string;
  status?: string;
  expirationDateFrom?: Date;
  expirationDateTo?: Date;
  limit?: number;
  offset?: number;
}

export interface ContractWithExpiration {
  id: string;
  entityType: string;
  entityUuid: string;
  contractType: string;
  currentVersion: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  versions: {
    id: string;
    versionId: number;
    expirationDate: Date | null;
    signed: boolean;
    startDate: Date | null;
  }[];
  // Dados da entidade (customer/supplier)
  entityName?: string;
  entityEmail?: string;
}

export interface ExpiringContract {
  contractId: string;
  entityType: string;
  entityUuid: string;
  entityName: string;
  contractType: string;
  expirationDate: Date;
  daysUntilExpiration: number;
  currentVersion: number;
  status: string;
  versionId: number;
  signed: boolean;
}
