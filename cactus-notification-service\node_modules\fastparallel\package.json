{"name": "fastparallel", "version": "2.4.1", "description": "Zero-overhead asynchronous parallel/each/map function call", "main": "parallel.js", "scripts": {"lint": "standard", "test": "tape test.js | faucet", "coverage": "nyc --reporter=lcov tape test.js; cat coverage/lcov.info | coveralls"}, "pre-commit": ["lint", "test"], "repository": {"type": "git", "url": "https://github.com/mcollina/fastparallel.git"}, "keywords": ["parallel", "fast", "async"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/mcollina/fastparallel/issues"}, "homepage": "https://github.com/mcollina/fastparallel", "devDependencies": {"async": "^3.1.0", "coveralls": "^3.0.5", "fastbench": "^1.0.1", "faucet": "0.0.1", "insync": "^2.1.1", "items": "^2.1.2", "neo-async": "^2.6.1", "nyc": "^14.1.1", "parallelize": "^3.0.1", "pre-commit": "^1.2.2", "standard": "^13.0.1", "tape": "^4.11.0"}, "dependencies": {"reusify": "^1.0.4", "xtend": "^4.0.2"}}