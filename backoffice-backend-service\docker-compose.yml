services:
  # Aplicação principal
  api:
    # image: node:20-alpine
    container_name: nest_boilerplate_api
    working_dir: /app
    build:
      dockerfile: Dockerfile
      context: .
      target: development
      ssh:
        - default
      args:
        GITHUB_TOKEN: ${GITHUB_TOKEN}
    volumes:
      - ./:/app
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/nest_boilerplate?schema=public
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USERNAME: guest
      RABBITMQ_PASSWORD: guest
      EMAIL_ENABLED: "false"
      OTEL_EXPORTER_OTLP_ENDPOINT: http://jaeger:4318
      JWT_SECRET: seu_jwt_secret
      JWT_EXPIRATION: "3600"
      # --- Keycloak ---
      KEYCLOAK_BASE_URL: http://keycloak:8080
      KEYCLOAK_REALM: master
      <PERSON><PERSON><PERSON><PERSON><PERSON>K_CLIENT_ID: backend-dev-client
      KEYCLOAK_CLIENT_SECRET: myclientsecret
      KEYCLOAK_ADMIN_USERNAME: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      jaeger:
        condition: service_started
    restart: unless-stopped
    networks:
      - nest_network

  postgres:
    image: postgres:15-alpine
    container_name: nest_boilerplate_postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: nest_boilerplate
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - nest_network

  rabbitmq:
    image: rabbitmq:3-management
    container_name: nest_boilerplate_rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - nest_network

  # OpenTelemetry & observability
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: nest_boilerplate_jaeger
    ports:
      - "16686:16686"  # UI
      - "14250:14250"  # gRPC
      - "14268:14268"  # HTTP
      - "4318:4318"    # OTLP
    restart: unless-stopped
    networks:
      - nest_network

  prometheus:
    image: prom/prometheus:latest
    container_name: nest_boilerplate_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./local/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    depends_on:
      api:
        condition: service_started
    restart: unless-stopped
    networks:
      - nest_network

  grafana:
    image: grafana/grafana:latest
    container_name: nest_boilerplate_grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      prometheus:
        condition: service_started
    restart: unless-stopped
    networks:
      - nest_network

  pgadmin:
    image: dpage/pgadmin4
    container_name: nest_boilerplate_pgadmin
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - nest_network

  keycloak:
    image: quay.io/keycloak/keycloak:26.2.0
    container_name: nest_boilerplate_keycloak
    command: ["start-dev"]
    environment:
      KC_BOOTSTRAP_ADMIN_USERNAME: admin
      KC_BOOTSTRAP_ADMIN_PASSWORD: admin
    ports:
      - "8080:8080"
    volumes:
      - keycloak_data:/opt/keycloak/data
    restart: unless-stopped
    networks:
      - nest_network

volumes:
  postgres_data:
  rabbitmq_data:
  grafana_data:
  pgadmin_data:
  keycloak_data:

networks:
  nest_network:
    driver: bridge
