{"name": "backend-boilerplate", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_ENV=test npm run start:dev", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "cross-env KEYCLOAK_BASE_URL=http://localhost:8080 jest --maxWorkers=2 --maxOldSpaceSize=4096", "test:unit": "jest --maxWorkers=2 --maxOldSpaceSize=4096 --testPathPattern=\"src/.*unit/.*\"", "test:integration": "jest --maxWorkers=2 --maxOldSpaceSize=4096 --testPathPattern=\"src/.*integration/.*\"", "test:e2e": "jest --maxWorkers=2 --maxOldSpaceSize=4096 --testPathPattern=\"src/.*e2e/.*\"", "test:watch": "jest --watch", "test:cov": "cross-env KEYCLOAK_BASE_URL=http://localhost:8080 jest --maxWorkers=2 --maxOldSpaceSize=4096 --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e:legacy": "jest --config ./test/jest-e2e.config.ts", "test:e2e:watch": "jest --config ./test/jest-e2e.config.ts --watch", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset --force", "prisma:clean": "echo y | rm -rf prisma/migrations/* && yarn prisma:reset", "prisma:deploy": "prisma migrate deploy", "prisma:workflow": "dotenv -e .env.test -- yarn prisma:clean && dotenv -e .env.test -- yarn prisma:migrate && dotenv -e .env.test -- yarn prisma:generate", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "prepare": "husky install", "setup": "npm install && docker compose down -v --rmi all --remove-orphans && npm run docker:up && docker pull testcontainers/ryuk:0.11.0 && sleep 5 && export $(cat .env.test | grep -v '^#' | xargs) && npx prisma migrate deploy --schema=prisma/schema.prisma", "setup:all": "npm run setup && sleep 20 && npx prisma db push && chmod +x scripts/keycloak-setup.sh && ./scripts/keycloak-setup.sh"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/client-ses": "^3.772.0", "@aws-sdk/client-sns": "^3.772.0", "@aws-sdk/client-sqs": "^3.772.0", "@aws-sdk/credential-providers": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.830.0", "@js-sdsl/ordered-map": "^4.4.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.12", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.7", "@nestjs/typeorm": "^11.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.57.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.202.0", "@opentelemetry/exporter-prometheus": "^0.200.0", "@opentelemetry/instrumentation-nestjs-core": "^0.45.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-node": "^2.0.1", "@prisma/client": "^6.9.0", "@prisma/engines": "^6.9.0", "@types/bcrypt": "^5.0.2", "@types/nodemailer": "^6.4.17", "@willsoto/nestjs-prometheus": "^6.0.2", "amqplib": "^0.10.5", "ansis": "^4.1.0", "axios": "^1.8.4", "backend-boilerplate": "file:", "backoffice-backend-common": "git+ssh://**************:PetrusSoftware/backoffice-backend-common.git", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "chokidar": "^4.0.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "keycloak-connect": "^26.1.1", "keycloak-js": "^26.1.5", "nest": "^0.1.6", "nodemailer": "^6.10.1", "opossum": "^8.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.14.1", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.21", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.1.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@testcontainers/postgresql": "^10.26.0", "@types/amqplib": "^0.10.7", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.13.11", "@types/opossum": "^8.1.8", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "cross-env": "^7.0.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.0", "prettier": "^3.4.2", "prisma": "^6.9.0", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "testcontainers": "^10.26.0", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"]}}