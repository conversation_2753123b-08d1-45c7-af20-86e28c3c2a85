import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { BankAccountUpsertData, Prisma } from '@prisma/client';

@Injectable()
export class PrismaBankAccountUpsertDataRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    data: Prisma.BankAccountUpsertDataCreateInput,
  ): Promise<BankAccountUpsertData> {
    return this.prisma.bankAccountUpsertData.create({
      data,
    });
  }
}
