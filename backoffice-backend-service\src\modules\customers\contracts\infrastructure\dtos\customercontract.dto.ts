import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString, IsUUID, MinLength, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { ContractStatus, ContractType } from '@prisma/client';

export class CreateCustomerContractDto {
  @ApiProperty({
    description: 'Identificador único ou nome para o contrato/arquivo.',
    example: 'contrato_servico_cliente.pdf',
  })
  @IsString()
  @MinLength(1)
  contractIdentifier: string;

  @ApiProperty({
    description: 'UUID do cliente ao qual este contrato pertence.',
    format: 'uuid'
  })
  @IsUUID()
  customerUuid: string;

  @ApiPropertyOptional({
    description: 'Data de início do contrato (opcional)',
    format: 'date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do contrato (opcional)',
    format: 'date',
    example: '2025-12-31',
  })
  @IsOptional()
  @IsString()
  expirationDate?: string;

  @ApiPropertyOptional({
    description: 'Tipo de contrato',
    enum: ContractType,
    example: ContractType.CERT_PLATFORM,
  })
  @IsOptional()
  @IsEnum(ContractType)
  contractType?: ContractType;

  @ApiPropertyOptional({
    description: 'Se o contrato foi assinado',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  signed?: boolean;

  @ApiPropertyOptional({
    description: 'Observações sobre o contrato',
    example: 'Contrato com cláusulas especiais de confidencialidade',
  })
  @IsOptional()
  @IsString()
  observations?: string;
}

export class CreateCustomerContractsDto {
  @ApiProperty({
    description: 'Lista de metadados dos contratos',
    type: [CreateCustomerContractDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomerContractDto)
  contracts: CreateCustomerContractDto[];
}

export class CustomerContractApiBodyDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    description: "Arquivos dos contratos a serem enviados. Deve haver um arquivo para cada objeto na string JSON 'contractsMetadata'."
  })
  files: any[];

  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada contrato. O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"contractIdentifier": "contrato_principal.pdf", "customerUuid": "40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4", "contractType": "CERT_PLATFORM", "signed": true, "startDate": "2024-01-01", "expirationDate": "2025-12-31", "observations": "Contrato com cláusulas especiais de confidencialidade"}]'
  })
  @IsString()
  contractsMetadata: string;
}

export class CustomerContractTextFormDataDto {
  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada contrato. O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"contractIdentifier": "contrato_principal.pdf", "customerUuid": "40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4", "contractType": "CERT_PLATFORM", "signed": true, "startDate": "2024-01-01", "expirationDate": "2025-12-31", "observations": "Contrato com cláusulas especiais de confidencialidade"}]'
  })
  @IsString()
  contractsMetadata: string;
}

export class CustomerContractSignPatchDto {
  @ApiProperty({ description: 'Indica se o contrato foi assinado', type: Boolean })
  @IsBoolean()
  isSigned: boolean;
}

export class CreateContractDto {
  @ApiProperty({ type: 'string', format: 'binary', description: 'Arquivo do contrato (será salvo como base64)', required: false })
  file?: any;

  @ApiProperty()
  isSigned: boolean;
}

export class ContractDto {
  @ApiProperty()
  uuid: string;

  @ApiProperty()
  customerUuid: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ description: 'Base64 do arquivo do contrato', required: false })
  url?: string;

  @ApiProperty()
  isSigned: boolean;

  @ApiProperty({ description: 'URL para download do contrato', required: false })
  downloadUrl?: string | null;

  @ApiProperty({ description: 'Nome do arquivo do contrato', required: false })
  fileName?: string | null;

  @ApiProperty({ description: 'Data de criação do contrato' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização do contrato' })
  updatedAt: Date;

  @ApiProperty({ description: 'Status do contrato' })
  status: ContractStatus;
}