import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.test' });
import { KeycloakService } from '../../keycloak.service';
import { KeycloakAdminUtils } from '../../keycloak.admin.utils';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../utils/request.utils.service';

describe('KeycloakService', () => {
  let configService: ConfigService;
  let keycloakAdminUtils: KeycloakAdminUtils;
  let requestUtilsService: RequestUtilsService;
  let service: KeycloakService;

  beforeEach(() => {
    configService = {
      get: jest.fn((key: string) => {
        const config = {
          KEYCLOAK_BASE_URL: 'http://localhost:8080',
          KEYCLOAK_REALM: 'test-realm',
          KEYCLOAK_CLIENT_ID: 'test-client',
          KEYCLOAK_CLIENT_SECRET: 'test-secret',
          KEYCLOAK_ADMIN_USERNAME: 'admin',
          KEYCLOAK_ADMIN_PASSWORD: 'admin-password',
        };
        return config[key];
      }),
    } as unknown as ConfigService;
    keycloakAdminUtils = new KeycloakAdminUtils(
      configService,
      {} as RequestUtilsService,
    );
    keycloakAdminUtils.getAdminToken = jest.fn().mockResolvedValue('token');
    keycloakAdminUtils.createUser = jest.fn().mockResolvedValue('userId');
    keycloakAdminUtils.assignRole = jest.fn();
    keycloakAdminUtils.removeRole = jest.fn();

    requestUtilsService = {
      executeWithRetry: jest.fn((fn: () => unknown) => fn()),
    } as unknown as RequestUtilsService;
    service = new KeycloakService(
      {} as Record<string, unknown>,
      keycloakAdminUtils,
      configService,
      requestUtilsService,
    );
  });

  it('should throw if config is missing', () => {
    const emptyConfigService = {
      get: jest.fn().mockReturnValue(undefined),
    } as unknown as ConfigService;

    expect(
      () =>
        new KeycloakService(
          {} as Record<string, unknown>,
          keycloakAdminUtils,
          emptyConfigService,
          requestUtilsService,
        ),
    ).toThrow();
  });

  it('should get token', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => ({
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 1000,
        token_type: 'bearer',
      }),
    );
    const result = await service.token('user', 'pass');
    expect(result.access_token).toBe('token');
  });

  it('should handle token error', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => {
        throw new Error('fail');
      },
    );
    await expect(service.token('user', 'pass')).rejects.toThrow();
  });

  it('should get client credentials token', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => ({
        access_token: 'token',
        expires_in: 1000,
        token_type: 'bearer',
      }),
    );
    const result = await service.clientCredentialsToken();
    expect(result.access_token).toBe('token');
  });

  it('should handle client credentials error', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => {
        throw new Error('fail');
      },
    );
    await expect(service.clientCredentialsToken()).rejects.toThrow();
  });

  it('should create user', async () => {
    const result = await service.createUser({});
    expect(result).toBe('userId');
  });

  it('should assign role', async () => {
    await service.assignRole('id', 'role');
    const assignRoleMock = jest.spyOn(keycloakAdminUtils, 'assignRole');
    expect(assignRoleMock).toBeCalled();
  });

  it('should remove role', async () => {
    await service.removeRole('id', 'role');
    const removeRoleMock = jest.spyOn(keycloakAdminUtils, 'removeRole');
    expect(removeRoleMock).toBeCalled();
  });
});
