// Setup global para testes
import { jest } from '@jest/globals';

// Mock do logger para evitar logs durante os testes
jest.mock('../shared/logger/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock das variáveis de ambiente
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_notifications';
process.env.BACKOFFICE_DATABASE_URL = 'postgresql://test:test@localhost:5432/test_backoffice';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.JWT_SECRET = 'test-secret-key-for-testing-purposes-only';
process.env.CONTRACT_EXPIRATION_DAYS_AHEAD = '30,15,7,1';
process.env.CONTRACT_CHECK_CRON = '0 * * * *';

// Configurar timeout global para testes
jest.setTimeout(10000);

// Limpar todos os mocks após cada teste
afterEach(() => {
  jest.clearAllMocks();
});

// Configurar mocks globais
beforeAll(() => {
  // Mock do console para testes mais limpos
  global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
});
