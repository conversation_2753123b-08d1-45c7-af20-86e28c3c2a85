import { NotificationController } from '../notification.controller';
import { NotificationService } from '../../services/notification.service';
import { NotificationListenerService } from '../../services/notification-listener.service';
import { FastifyRequest, FastifyReply } from 'fastify';

// Mock dos serviços
jest.mock('../../services/notification.service');
jest.mock('../../services/notification-listener.service');

describe('NotificationController', () => {
  let controller: NotificationController;
  let mockNotificationService: jest.Mocked<NotificationService>;
  let mockListenerService: jest.Mocked<NotificationListenerService>;
  let mockRequest: Partial<FastifyRequest>;
  let mockReply: Partial<FastifyReply>;

  beforeEach(() => {
    mockNotificationService = new NotificationService() as jest.Mocked<NotificationService>;
    mockListenerService = new NotificationListenerService() as jest.Mocked<NotificationListenerService>;
    controller = new NotificationController(mockListenerService);
    
    // Substituir a instância do serviço no controller
    (controller as any).notificationService = mockNotificationService;

    mockRequest = {
      headers: { 'x-user-id': 'test-user' },
      query: {},
      params: {},
      body: {},
    };

    mockReply = {
      send: jest.fn(),
      status: jest.fn().mockReturnThis(),
      raw: {
        writeHead: jest.fn(),
        write: jest.fn(),
        on: jest.fn(),
      },
    };

    jest.clearAllMocks();
  });

  describe('getNotifications', () => {
    it('should return notifications successfully', async () => {
      const mockNotifications = [
        {
          id: 'notification-1',
          userId: 'test-user',
          type: 'CONTRACT_EXPIRING',
          title: 'Test',
          message: 'Message',
          data: {},
          isRead: false,
          readAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockNotificationService.findNotifications.mockResolvedValue(mockNotifications);
      mockNotificationService.getUnreadCount.mockResolvedValue(1);

      await controller.getNotifications(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockNotificationService.findNotifications).toHaveBeenCalledWith({
        userId: 'test-user',
        type: undefined,
        isRead: undefined,
        limit: 20,
        offset: 0,
      });

      expect(mockReply.send).toHaveBeenCalledWith({
        notifications: mockNotifications,
        unreadCount: 1,
        pagination: {
          limit: 20,
          offset: 0,
          total: 1,
        },
      });
    });

    it('should handle query parameters correctly', async () => {
      mockRequest.query = {
        type: 'CONTRACT_EXPIRING',
        isRead: 'true',
        limit: '10',
        offset: '5',
      };

      mockNotificationService.findNotifications.mockResolvedValue([]);
      mockNotificationService.getUnreadCount.mockResolvedValue(0);

      await controller.getNotifications(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockNotificationService.findNotifications).toHaveBeenCalledWith({
        userId: 'test-user',
        type: 'CONTRACT_EXPIRING',
        isRead: true,
        limit: 10,
        offset: 5,
      });
    });
  });

  describe('markAsRead', () => {
    it('should mark notification as read successfully', async () => {
      const mockNotification = {
        id: 'notification-1',
        userId: 'test-user',
        type: 'CONTRACT_EXPIRING',
        title: 'Test',
        message: 'Message',
        data: {},
        isRead: true,
        readAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRequest.params = { notificationId: 'notification-1' };
      mockNotificationService.markAsRead.mockResolvedValue(mockNotification);

      await controller.markAsRead(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockNotificationService.markAsRead).toHaveBeenCalledWith(
        'notification-1',
        'test-user'
      );

      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        notification: mockNotification,
      });
    });
  });

  describe('markMultipleAsRead', () => {
    it('should mark multiple notifications as read', async () => {
      mockRequest.body = {
        notificationIds: ['notification-1', 'notification-2'],
      };

      mockNotificationService.markMultipleAsRead.mockResolvedValue(2);

      await controller.markMultipleAsRead(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockNotificationService.markMultipleAsRead).toHaveBeenCalledWith(
        ['notification-1', 'notification-2'],
        'test-user'
      );

      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        markedCount: 2,
      });
    });

    it('should return error for invalid input', async () => {
      mockRequest.body = {
        notificationIds: 'invalid',
      };

      await controller.markMultipleAsRead(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.status).toHaveBeenCalledWith(400);
      expect(mockReply.send).toHaveBeenCalledWith({
        error: 'notificationIds must be an array',
      });
    });
  });

  describe('getUserSettings', () => {
    it('should return user settings', async () => {
      const mockSettings = {
        id: 'settings-1',
        userId: 'test-user',
        contractExpiration: true,
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockNotificationService.getUserSettings.mockResolvedValue(mockSettings);

      await controller.getUserSettings(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockNotificationService.getUserSettings).toHaveBeenCalledWith('test-user');
      expect(mockReply.send).toHaveBeenCalledWith({
        settings: mockSettings,
      });
    });
  });

  describe('updateUserSettings', () => {
    it('should update user settings', async () => {
      const mockSettings = {
        id: 'settings-1',
        userId: 'test-user',
        contractExpiration: false,
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRequest.body = {
        contractExpiration: false,
        pushNotifications: false,
      };

      mockNotificationService.updateUserSettings.mockResolvedValue(mockSettings);

      await controller.updateUserSettings(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockNotificationService.updateUserSettings).toHaveBeenCalledWith({
        userId: 'test-user',
        contractExpiration: false,
        pushNotifications: false,
      });

      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        settings: mockSettings,
      });
    });
  });

  describe('getUnreadCount', () => {
    it('should return unread count', async () => {
      mockNotificationService.getUnreadCount.mockResolvedValue(3);

      await controller.getUnreadCount(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockNotificationService.getUnreadCount).toHaveBeenCalledWith('test-user');
      expect(mockReply.send).toHaveBeenCalledWith({
        unreadCount: 3,
      });
    });
  });

  describe('getConnectionStats', () => {
    it('should return connection statistics', async () => {
      const mockStats = {
        totalConnections: 5,
        userConnections: {
          'user-1': 2,
          'user-2': 3,
        },
      };

      mockListenerService.getConnectionStats.mockReturnValue(mockStats);

      await controller.getConnectionStats(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockListenerService.getConnectionStats).toHaveBeenCalled();
      expect(mockReply.send).toHaveBeenCalledWith({
        stats: mockStats,
      });
    });
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      const mockStats = {
        totalConnections: 2,
        userConnections: {},
      };

      mockListenerService.getConnectionStats.mockReturnValue(mockStats);

      await controller.healthCheck(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockReply.send).toHaveBeenCalledWith({
        status: 'healthy',
        timestamp: expect.any(String),
        connections: 2,
        service: 'cactus-notification-service',
      });
    });
  });
});
