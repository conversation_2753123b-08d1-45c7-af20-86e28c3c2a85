import { prismaBackoffice } from '../../../shared/database/prisma';
import { logger } from '../../../shared/logger/logger';
import { ContractWithExpiration, ExpiringContract } from '../../../shared/types/contract.types';

export class ContractReaderService {
  /**
   * Busca contratos que estão vencendo nos próximos dias especificados
   */
  async findExpiringContracts(daysAhead: number[]): Promise<ExpiringContract[]> {
    try {
      logger.info(`Searching for contracts expiring in ${daysAhead.join(', ')} days`);

      const now = new Date();
      const maxDays = Math.max(...daysAhead);
      const maxDate = new Date(now.getTime() + maxDays * 24 * 60 * 60 * 1000);

      // Buscar contratos com versões que têm data de expiração
      const contracts = await prismaBackoffice.backofficeContract.findMany({
        where: {
          status: 'APPROVED', // Apenas contratos aprovados
          versions: {
            some: {
              expirationDate: {
                gte: now,
                lte: maxDate,
              },
              signed: true, // Apenas versões assinadas
            },
          },
        },
        include: {
          versions: {
            where: {
              expirationDate: {
                gte: now,
                lte: maxDate,
              },
              signed: true,
            },
            orderBy: {
              versionId: 'desc',
            },
            take: 1, // Pegar apenas a versão mais recente
          },
        },
      });

      const expiringContracts: ExpiringContract[] = [];

      for (const contract of contracts) {
        if (contract.versions.length === 0) continue;

        const version = contract.versions[0];
        if (!version.expirationDate) continue;

        const daysUntilExpiration = Math.ceil(
          (version.expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
        );

        // Verificar se está nos dias especificados
        if (daysAhead.includes(daysUntilExpiration)) {
          // Buscar informações da entidade (customer)
          let entityName = 'Unknown Entity';
          
          if (contract.entityType === 'CLIENT') {
            try {
              const customer = await prismaBackoffice.backofficeCustomer.findUnique({
                where: { uuid: contract.entityUuid },
                select: { razaoSocial: true },
              });
              entityName = customer?.razaoSocial || entityName;
            } catch (error) {
              logger.warn(`Failed to fetch customer info for ${contract.entityUuid}:`, error);
            }
          }

          expiringContracts.push({
            contractId: contract.id,
            entityType: contract.entityType,
            entityUuid: contract.entityUuid,
            entityName,
            contractType: contract.contractType,
            expirationDate: version.expirationDate,
            daysUntilExpiration,
            currentVersion: contract.currentVersion,
            status: contract.status,
            versionId: version.versionId,
            signed: version.signed,
          });
        }
      }

      logger.info(`Found ${expiringContracts.length} expiring contracts`);
      return expiringContracts;

    } catch (error) {
      logger.error('Error finding expiring contracts:', error);
      throw error;
    }
  }

  /**
   * Busca contratos que já expiraram
   */
  async findExpiredContracts(): Promise<ExpiringContract[]> {
    try {
      logger.info('Searching for expired contracts');

      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const contracts = await prismaBackoffice.backofficeContract.findMany({
        where: {
          status: 'APPROVED',
          versions: {
            some: {
              expirationDate: {
                gte: yesterday,
                lt: now,
              },
              signed: true,
            },
          },
        },
        include: {
          versions: {
            where: {
              expirationDate: {
                gte: yesterday,
                lt: now,
              },
              signed: true,
            },
            orderBy: {
              versionId: 'desc',
            },
            take: 1,
          },
        },
      });

      const expiredContracts: ExpiringContract[] = [];

      for (const contract of contracts) {
        if (contract.versions.length === 0) continue;

        const version = contract.versions[0];
        if (!version.expirationDate) continue;

        let entityName = 'Unknown Entity';
        
        if (contract.entityType === 'CLIENT') {
          try {
            const customer = await prismaBackoffice.backofficeCustomer.findUnique({
              where: { uuid: contract.entityUuid },
              select: { razaoSocial: true },
            });
            entityName = customer?.razaoSocial || entityName;
          } catch (error) {
            logger.warn(`Failed to fetch customer info for ${contract.entityUuid}:`, error);
          }
        }

        expiredContracts.push({
          contractId: contract.id,
          entityType: contract.entityType,
          entityUuid: contract.entityUuid,
          entityName,
          contractType: contract.contractType,
          expirationDate: version.expirationDate,
          daysUntilExpiration: 0,
          currentVersion: contract.currentVersion,
          status: contract.status,
          versionId: version.versionId,
          signed: version.signed,
        });
      }

      logger.info(`Found ${expiredContracts.length} expired contracts`);
      return expiredContracts;

    } catch (error) {
      logger.error('Error finding expired contracts:', error);
      throw error;
    }
  }

  /**
   * Testa a conexão com a database do backoffice
   */
  async testConnection(): Promise<boolean> {
    try {
      await prismaBackoffice.$queryRaw`SELECT 1`;
      logger.info('Backoffice database connection successful');
      return true;
    } catch (error) {
      logger.error('Backoffice database connection failed:', error);
      return false;
    }
  }
}
