import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Customer } from '@prisma/client';

@Injectable()
export class PrismaCustomerRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findByUserId(userId: string): Promise<Customer | null> {
    return this.prisma.customer.findUnique({
      where: { userId },
    });
  }

  async findByUuid(uuid: string): Promise<Customer | null> {
    return this.prisma.customer.findUnique({
      where: { uuid },
    });
  }
}
