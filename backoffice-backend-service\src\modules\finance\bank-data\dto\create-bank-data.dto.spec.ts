import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { CreateBankDataDto } from './create-bank-data.dto';
import {
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

describe('CreateBankDataDto', () => {
  describe('Valid inputs', () => {
    it('should validate with all required fields', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: '<PERSON>',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: false,
        status: BankDataStatus.ACTIVE,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal required fields only', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.SAVINGS,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with bank code in format 123-4', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001-2',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with different account types', async () => {
      const accountTypes = [BankAccountType.CHECKING, BankAccountType.SAVINGS];

      for (const accountType of accountTypes) {
        const dto = plainToClass(CreateBankDataDto, {
          bankName: 'Banco do Brasil S.A.',
          bankCode: '001',
          accountType,
          agencyNumber: '1234',
          accountNumber: '123456',
          accountDigit: '7',
          accountHolderName: 'João da Silva',
          accountHolderDocument: '***********',
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate with different PIX key types', async () => {
      const pixKeyTypes = [
        BankPixKeyType.EMAIL,
        BankPixKeyType.PHONE,
        BankPixKeyType.CPF,
        BankPixKeyType.CNPJ,
        BankPixKeyType.RANDOM,
      ];

      for (const pixKeyType of pixKeyTypes) {
        const dto = plainToClass(CreateBankDataDto, {
          bankName: 'Banco do Brasil S.A.',
          bankCode: '001',
          accountType: BankAccountType.CHECKING,
          agencyNumber: '1234',
          accountNumber: '123456',
          accountDigit: '7',
          accountHolderName: 'João da Silva',
          accountHolderDocument: '***********',
          pixKey: '<EMAIL>',
          pixKeyType,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate with CNPJ document (14 digits)', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'Empresa LTDA',
        accountHolderDocument: '**************',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('Invalid inputs', () => {
    it('should fail validation when bankName is empty', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: '',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankNameError = errors.find(
        (error) => error.property === 'bankName',
      );
      expect(bankNameError).toBeDefined();
      expect(Object.values(bankNameError?.constraints || {})).toContain(
        'Bank name is required',
      );
    });

    it('should fail validation when bankCode is invalid format', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: 'ABC',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankCodeError = errors.find(
        (error) => error.property === 'bankCode',
      );
      expect(bankCodeError).toBeDefined();
      expect(Object.values(bankCodeError?.constraints || {})).toContain(
        'Bank code must contain only numbers or be in the format 123-4',
      );
    });

    it('should fail validation when agencyNumber contains non-numeric characters', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '12A4',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const agencyNumberError = errors.find(
        (error) => error.property === 'agencyNumber',
      );
      expect(agencyNumberError).toBeDefined();
      expect(Object.values(agencyNumberError?.constraints || {})).toContain(
        'Agency number must contain only numbers',
      );
    });

    it('should fail validation when accountNumber contains non-numeric characters', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123A56',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const accountNumberError = errors.find(
        (error) => error.property === 'accountNumber',
      );
      expect(accountNumberError).toBeDefined();
      expect(Object.values(accountNumberError?.constraints || {})).toContain(
        'Account number must contain only numbers',
      );
    });

    it('should fail validation when accountDigit contains non-numeric characters', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: 'X',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const accountDigitError = errors.find(
        (error) => error.property === 'accountDigit',
      );
      expect(accountDigitError).toBeDefined();
      expect(Object.values(accountDigitError?.constraints || {})).toContain(
        'Account digit must contain only numbers',
      );
    });

    it('should fail validation when accountHolderDocument is too short', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '*********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const documentError = errors.find(
        (error) => error.property === 'accountHolderDocument',
      );
      expect(documentError).toBeDefined();
      expect(Object.values(documentError?.constraints || {})).toContain(
        'Account holder document must contain only numbers and be between 11 and 14 digits long',
      );
    });

    it('should fail validation when accountHolderDocument is too long', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********2345',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const documentError = errors.find(
        (error) => error.property === 'accountHolderDocument',
      );
      expect(documentError).toBeDefined();
      expect(Object.values(documentError?.constraints || {})).toContain(
        'Account holder document must be at most 14 characters long',
      );
    });

    it('should fail validation when bankName exceeds max length', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'a'.repeat(101),
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankNameError = errors.find(
        (error) => error.property === 'bankName',
      );
      expect(bankNameError).toBeDefined();
      expect(Object.values(bankNameError?.constraints || {})).toContain(
        'Bank name must be at most 100 characters long',
      );
    });

    it('should fail validation for invalid account type', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: 'INVALID_TYPE' as BankAccountType,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const accountTypeError = errors.find(
        (error) => error.property === 'accountType',
      );
      expect(accountTypeError).toBeDefined();
      expect(Object.values(accountTypeError?.constraints || {})).toContain(
        'Account type must be a valid value',
      );
    });

    it('should fail validation for invalid PIX key type', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: 'INVALID_TYPE' as BankPixKeyType,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const pixKeyTypeError = errors.find(
        (error) => error.property === 'pixKeyType',
      );
      expect(pixKeyTypeError).toBeDefined();
      expect(Object.values(pixKeyTypeError?.constraints || {})).toContain(
        'PIX key type must be a valid value',
      );
    });

    it('should fail validation for invalid status', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
        status: 'INVALID_STATUS' as BankDataStatus,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const statusError = errors.find((error) => error.property === 'status');
      expect(statusError).toBeDefined();
      expect(Object.values(statusError?.constraints || {})).toContain(
        'Status must be a valid enum value',
      );
    });
  });

  describe('Optional fields', () => {
    it('should validate when agencyDigit is not provided', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate when pixKey and pixKeyType are not provided', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate when isDigitalBank is not provided', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate when status is not provided', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
