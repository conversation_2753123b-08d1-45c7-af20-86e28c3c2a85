import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsOptional,
  IsInt,
  Min,
  Max,
  IsEnum,
  IsString,
  MaxLength,
  IsBoolean,
  IsDateString,
} from 'class-validator';
import {
  EntityType,
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

export class GetBankDataQueryDto {
  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must be at most 100' })
  limit?: number = 10;

  @ApiProperty({
    description: 'Filter by employee ID',
    example: 123,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Employee ID must be an integer' })
  employeeId?: number;

  @ApiProperty({
    description: 'Filter by bank data status',
    enum: BankDataStatus,
    example: BankDataStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankDataStatus, {
    message: 'Status must be a valid enum value',
  })
  status?: BankDataStatus;

  @ApiProperty({
    description: 'Filter by entity type',
    enum: EntityType,
    example: EntityType.COLLABORATE,
    required: false,
  })
  @IsOptional()
  @IsEnum(EntityType, {
    message: 'Entity type must be a valid enum value',
  })
  entityType?: EntityType;

  @ApiProperty({
    description: 'Filter by bank name (partial match)',
    example: 'Banco do Brasil',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Bank name must be a string' })
  @MaxLength(100, { message: 'Bank name must be at most 100 characters long' })
  bankName?: string;

  @ApiProperty({
    description: 'Filter by bank code',
    example: '001',
    maxLength: 10,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Bank code must be a string' })
  @MaxLength(10, { message: 'Bank code must be at most 10 characters long' })
  bankCode?: string;

  @ApiProperty({
    description: 'Filter by account type',
    enum: BankAccountType,
    example: BankAccountType.CHECKING,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankAccountType, {
    message: 'Account type must be a valid enum value',
  })
  accountType?: BankAccountType;

  @ApiProperty({
    description: 'Filter by agency number',
    example: '1234',
    maxLength: 10,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Agency number must be a string' })
  @MaxLength(10, {
    message: 'Agency number must be at most 10 characters long',
  })
  agencyNumber?: string;

  @ApiProperty({
    description: 'Filter by agency digit',
    example: '5',
    maxLength: 5,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Agency digit must be a string' })
  @MaxLength(5, { message: 'Agency digit must be at most 5 characters long' })
  agencyDigit?: string;

  @ApiProperty({
    description: 'Filter by account number',
    example: '123456',
    maxLength: 10,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account number must be a string' })
  @MaxLength(10, {
    message: 'Account number must be at most 10 characters long',
  })
  accountNumber?: string;

  @ApiProperty({
    description: 'Filter by account digit',
    example: '7',
    maxLength: 5,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account digit must be a string' })
  @MaxLength(5, { message: 'Account digit must be at most 5 characters long' })
  accountDigit?: string;

  @ApiProperty({
    description: 'Filter by account holder name (partial match)',
    example: 'João da Silva',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account holder name must be a string' })
  @MaxLength(100, {
    message: 'Account holder name must be at most 100 characters long',
  })
  accountHolderName?: string;

  @ApiProperty({
    description: 'Filter by account holder document',
    example: '***********',
    maxLength: 14,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Account holder document must be a string' })
  @MaxLength(14, {
    message: 'Account holder document must be at most 14 characters long',
  })
  accountHolderDocument?: string;

  @ApiProperty({
    description: 'Filter by PIX key (partial match)',
    example: '<EMAIL>',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'PIX key must be a string' })
  @MaxLength(100, { message: 'PIX key must be at most 100 characters long' })
  pixKey?: string;

  @ApiProperty({
    description: 'Filter by PIX key type',
    enum: BankPixKeyType,
    example: BankPixKeyType.EMAIL,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankPixKeyType, {
    message: 'PIX key type must be a valid enum value',
  })
  pixKeyType?: BankPixKeyType;

  @ApiProperty({
    description: 'Filter by digital bank status',
    example: false,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => value === 'true')
  @IsBoolean({ message: 'Digital bank indicator must be a boolean value' })
  isDigitalBank?: boolean;

  @ApiProperty({
    description: 'Filter by creation date (ISO string)',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created at must be a valid date string' })
  createdAt?: string;

  @ApiProperty({
    description: 'Filter by update date (ISO string)',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Updated at must be a valid date string' })
  updatedAt?: string;
}
