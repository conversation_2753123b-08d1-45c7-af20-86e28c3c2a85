<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.7">
  <diagram name="Página-1" id="tWyUKoPeDjIZkkxUIhZB">
    <mxGraphModel dx="1240" dy="646" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-1" value="&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Queue de leitura de contratos&lt;br&gt;&lt;/span&gt;CRON - Bull MQ (com repeat)&lt;br&gt;Cactus Notif. Service" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="380" y="50" width="190" height="190" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-2" value="PostgreSQL &lt;br&gt;DB Backoffice" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="10" y="110" width="230" height="90" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-5" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.996;exitY=0.448;exitDx=0;exitDy=0;exitPerimeter=0;entryX=-0.005;entryY=0.527;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="sTmvFwNIdRuX3UqBM9UM-2" target="sTmvFwNIdRuX3UqBM9UM-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="160" as="sourcePoint" />
            <mxPoint x="380" y="110" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-6" value="PostgreSQL&amp;nbsp;&lt;br&gt;Cactus Notif. Service" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="380" width="230" height="90" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-7" value="DB HML&lt;br&gt;Consulta de contratos vencendo" style="text;whiteSpace=wrap;html=1;align=center;" vertex="1" parent="1">
          <mxGeometry x="250" y="90" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="sTmvFwNIdRuX3UqBM9UM-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="240" as="sourcePoint" />
            <mxPoint x="475" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-16" value="Grava notificação local&lt;br&gt;&lt;br&gt;Gera um NOTIFY" style="text;whiteSpace=wrap;html=1;align=center;" vertex="1" parent="1">
          <mxGeometry x="290" y="380" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-17" value="&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Queue de salvamento de notificações&lt;br&gt;&lt;/span&gt;Bull MQ&amp;nbsp;&lt;br&gt;Cactus Notif. Service" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="380" y="330" width="190" height="190" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.526;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.994;entryY=0.556;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="sTmvFwNIdRuX3UqBM9UM-17" target="sTmvFwNIdRuX3UqBM9UM-6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="470" as="sourcePoint" />
            <mxPoint x="370" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-22" value="Server" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="585" width="220" height="140" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-23" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="sTmvFwNIdRuX3UqBM9UM-6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="155" y="580" as="sourcePoint" />
            <mxPoint x="160" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-26" value="LISTNER de novas notificações&lt;br&gt;&lt;br&gt;Atualiza &#39;marcado como lido&#39;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="160" y="510" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-29" value="Web Client" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="585" width="220" height="140" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-30" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="sTmvFwNIdRuX3UqBM9UM-22" target="sTmvFwNIdRuX3UqBM9UM-29">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="700" as="sourcePoint" />
            <mxPoint x="330" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-31" value="Get - SSE&lt;br&gt;&lt;br&gt;Patch - marcar como lido&lt;br&gt;&lt;br&gt;Gerenciamento do tipo e locais a serem notificados" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="550" width="165" height="110" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.53;exitY=1.005;exitDx=0;exitDy=0;entryX=0.4;entryY=0.1;entryDx=0;entryDy=0;entryPerimeter=0;exitPerimeter=0;" edge="1" parent="1" source="sTmvFwNIdRuX3UqBM9UM-22" target="sTmvFwNIdRuX3UqBM9UM-34">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="870" as="sourcePoint" />
            <mxPoint x="430" y="810" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-34" value="Email&lt;br&gt;Versão futura" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="810" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-35" value="App&lt;br&gt;Versão futura" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="30" y="810" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sTmvFwNIdRuX3UqBM9UM-36" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.4;entryY=0.1;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.527;exitY=1.005;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="sTmvFwNIdRuX3UqBM9UM-22" target="sTmvFwNIdRuX3UqBM9UM-35">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="800" as="sourcePoint" />
            <mxPoint x="160" y="750" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
