import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { healthService } from './health.service';

async function healthPlugin(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // Liveness probe - verifica se a aplicação está viva
  fastify.get('/health/live', async (request, reply) => {
    const result = await healthService.isLive();
    
    reply
      .code(200)
      .header('Content-Type', 'application/json')
      .send(result);
  });

  // Readiness probe - verifica se a aplicação está pronta
  fastify.get('/health/ready', async (request, reply) => {
    const result = await healthService.isReady();
    
    const statusCode = result.status === 'healthy' ? 200 : 503;
    
    reply
      .code(statusCode)
      .header('Content-Type', 'application/json')
      .send(result);
  });

  // Endpoint de health simples (mantém compatibilidade)
  fastify.get('/health', async (request, reply) => {
    const result = await healthService.isReady();
    
    // Formato simplificado para compatibilidade
    const simpleResult = {
      status: result.status === 'healthy' ? 'ok' : 'error',
      timestamp: result.timestamp,
      environment: process.env.NODE_ENV,
      port: process.env.PORT,
      checks: result.checks,
    };
    
    const statusCode = result.status === 'healthy' ? 200 : 503;
    
    reply
      .code(statusCode)
      .header('Content-Type', 'application/json')
      .send(simpleResult);
  });
}

export default fp(healthPlugin, {
  name: 'health-plugin',
});
