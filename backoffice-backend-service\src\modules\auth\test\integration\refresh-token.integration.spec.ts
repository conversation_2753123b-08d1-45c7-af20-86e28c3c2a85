import { Test } from '@nestjs/testing';
import { AuthController } from '../../auth.controller';
import { AuthService } from '../../auth.service';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../../infrastructure/utils/request.utils.service';
import {
  AUTH_TEST_CONFIG,
  mockConfigService,
  mockRequestUtilsService,
  createTestUser,
  resetAllMocks,
} from './auth-test.config';
import { JwtService } from '@nestjs/jwt';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { EventsTestModule } from '../../../../infrastructure/events/test/events-test.module';
import { UsersService } from '../../../users/users.service';
import { RefreshTokenDto } from '../../dto/refresh-token.dto';
import { EmployeeService } from '../../../finance/employee/employee.service';
import { CustomerRepository } from '../../../customers/infrastructure/repositories/customer.repository';
import { SupplierAuthService } from '../../supplier-auth/supplier-auth.service';
import { PrismaSupplierRepository } from '../../../../infrastructure/repositories/prisma-supplier.repository';
import { PrismaEmployeeRepository } from '../../../../infrastructure/repositories/prisma-employee.repository';
// import { EmailService } from '../../../../infrastructure/email/nodemailer-email.service.interface';

describe('Auth Refresh Token Integration Tests', () => {
  let controller: AuthController;
  let _authService: AuthService;
  let keycloakService: KeycloakService;
  let testUser: {
    email: string;
    password: string;
    name: string;
    accessToken: string | null;
    refreshToken: string | null;
    keycloakId: string;
  };

  const mockUserRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByEmail: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    updateUserKeycloakId: jest.fn(),
  };

  const mockUsersService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockImplementation(() => 'mock-jwt-token'),
    decode: jest.fn(),
  };

  const mockKeycloakIdentityProvider = {
    registerUser: jest.fn(),
    assignUserRoles: jest.fn(),
    authenticate: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    getUserInfo: jest.fn(),
    keycloakAdminUtils: {
      getAdminAuthHeaders: jest.fn().mockResolvedValue({
        Authorization: 'Bearer mock-token',
        'Content-Type': 'application/json',
      }),
    },
  };

  const mockEventPublisher = {
    publish: jest.fn(),
  };

  const mockEmailService = {
    sendResetPasswordEmail: jest.fn(),
  };

  const mockSupplierAuthService = {
    // Add any methods that might be called
  };

  const mockPrismaSupplierRepository = {
    // Add any methods that might be called
  };

  const mockPrismaEmployeeRepository = {
    // Add any methods that might be called
  };

  const mockUsersOtpRepository = {
    create: jest.fn(),
    findByEmail: jest.fn(),
    markAsUsed: jest.fn(),
    deleteExpiredTokens: jest.fn(),
  };

  beforeEach(async () => {
    // Reset all mocks before each test
    resetAllMocks();

    // Create a new test user for each test
    testUser = createTestUser();
    testUser.accessToken = AUTH_TEST_CONFIG.mock.accessToken;
    testUser.refreshToken = AUTH_TEST_CONFIG.mock.refreshToken;

    const moduleRef = await Test.createTestingModule({
      imports: [EventsTestModule],
      controllers: [AuthController],
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: {
            create: jest.fn(),
            findByToken: jest.fn(),
            markAsUsed: jest.fn(),
            deleteExpiredTokens: jest.fn(),
          },
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: KeycloakService,
          useFactory: () => ({
            token: jest.fn().mockResolvedValue({
              access_token: AUTH_TEST_CONFIG.mock.accessToken,
              refresh_token: AUTH_TEST_CONFIG.mock.refreshToken,
              expires_in: 300,
            }),
            refreshToken: jest.fn().mockResolvedValue({
              access_token: AUTH_TEST_CONFIG.mock.accessToken,
            }),
            logout: jest.fn().mockResolvedValue(undefined),
            validateToken: jest.fn().mockResolvedValue(true),
            getUserInfo: jest.fn().mockResolvedValue({
              sub: testUser.keycloakId,
              preferred_username: testUser.email,
              email: testUser.email,
              name: testUser.name,
            }),
            getBaseUrl: jest
              .fn()
              .mockReturnValue(AUTH_TEST_CONFIG.keycloak.baseUrl),
            getRealm: jest
              .fn()
              .mockReturnValue(AUTH_TEST_CONFIG.keycloak.realm),
          }),
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakIdentityProvider,
        },
        {
          provide: EventPublisherService,
          useValue: mockEventPublisher,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RequestUtilsService,
          useValue: mockRequestUtilsService,
        },
        {
          provide: EmployeeService,
          useValue: {},
        },
        {
          provide: CustomerRepository,
          useValue: {},
        },
        {
          provide: SupplierAuthService,
          useValue: mockSupplierAuthService,
        },
        {
          provide: PrismaSupplierRepository,
          useValue: mockPrismaSupplierRepository,
        },
        {
          provide: PrismaEmployeeRepository,
          useValue: mockPrismaEmployeeRepository,
        },
        {
          provide: 'UsersOtpRepository',
          useValue: mockUsersOtpRepository,
        },
      ],
    }).compile();

    controller = moduleRef.get<AuthController>(AuthController);
    _authService = moduleRef.get<AuthService>(AuthService);
    keycloakService = moduleRef.get<KeycloakService>(KeycloakService);
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const refreshDto: RefreshTokenDto = {
        refresh_token: testUser.refreshToken as string,
      };

      const result = await controller.refreshToken(refreshDto);

      expect(result).toEqual({
        access_token: AUTH_TEST_CONFIG.mock.accessToken,
      });
    });

    it('should reject invalid refresh token', async () => {
      const refreshDto: RefreshTokenDto = {
        refresh_token: 'invalid-refresh-token',
      };

      jest
        .spyOn(keycloakService, 'refreshToken')
        .mockRejectedValueOnce(new Error('Invalid refresh token'));

      await expect(controller.refreshToken(refreshDto)).rejects.toThrow(
        'Token de atualização inválido',
      );
    });
  });
});
