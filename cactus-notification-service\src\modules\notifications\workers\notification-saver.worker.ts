import { Worker, Job } from 'bullmq';
import Redis from 'ioredis';
import { NotificationService } from '../services/notification.service';
import { logger } from '../../../shared/logger/logger';
import { connectDatabases } from '../../../shared/database/prisma';
import { ExpiringContract } from '../../../shared/types/contract.types';
import { NotificationType } from '@prisma/client';

// Configuração do Redis
const redisConnection = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  maxRetriesPerRequest: 3,
});

const notificationService = new NotificationService();

// Dados do job de processamento de notificações
interface NotificationJobData {
  contract: ExpiringContract;
  notificationType: 'CONTRACT_EXPIRING' | 'CONTRACT_EXPIRED';
  userId?: string; // Se não fornecido, notificar todos os usuários relevantes
}

// Worker para processar notificações
const notificationWorker = new Worker(
  'notification-processing',
  async (job: Job<NotificationJobData>) => {
    logger.info(`Processing notification job: ${job.name}`, { jobId: job.id });

    try {
      const { contract, notificationType, userId } = job.data;

      // Determinar usuários que devem receber a notificação
      const userIds = userId ? [userId] : await getUsersForContract(contract);

      const notifications = [];

      for (const targetUserId of userIds) {
        try {
          // Gerar título e mensagem da notificação
          const { title, message } = generateNotificationContent(contract, notificationType);

          // Criar a notificação
          const notification = await notificationService.createNotification({
            userId: targetUserId,
            type: notificationType as NotificationType,
            title,
            message,
            data: {
              contractId: contract.contractId,
              entityName: contract.entityName,
              entityUuid: contract.entityUuid,
              contractType: contract.contractType,
              expirationDate: contract.expirationDate.toISOString(),
              daysUntilExpiration: contract.daysUntilExpiration,
              currentVersion: contract.currentVersion,
              status: contract.status,
            },
          });

          notifications.push(notification);
          logger.info(`Notification created for user ${targetUserId}`, { notificationId: notification.id });

        } catch (error) {
          logger.error(`Failed to create notification for user ${targetUserId}:`, error);
          // Continuar processando outros usuários mesmo se um falhar
        }
      }

      return {
        success: true,
        notificationsCreated: notifications.length,
        contractId: contract.contractId,
        notificationType,
      };

    } catch (error) {
      logger.error('Error in notification worker:', error);
      throw error;
    }
  },
  {
    connection: redisConnection,
    concurrency: 5, // Processar até 5 jobs simultaneamente
  }
);

// Event listeners
notificationWorker.on('completed', (job) => {
  logger.info(`Notification job completed: ${job.id}`, job.returnvalue);
});

notificationWorker.on('failed', (job, err) => {
  logger.error(`Notification job failed: ${job?.id}`, err);
});

notificationWorker.on('error', (err) => {
  logger.error('Notification worker error:', err);
});

/**
 * Determinar quais usuários devem receber notificação para um contrato
 * Por enquanto, retorna usuários padrão. Pode ser expandido para buscar
 * usuários relacionados ao contrato/entidade específica.
 */
async function getUsersForContract(contract: ExpiringContract): Promise<string[]> {
  // TODO: Implementar lógica para determinar usuários baseado no contrato
  // Por exemplo: buscar usuários responsáveis pela entidade, administradores, etc.
  
  // Por enquanto, retornar usuários padrão
  const defaultUsers = ['admin-user-id']; // Pode vir de variável de ambiente
  
  logger.info(`Determined users for contract ${contract.contractId}:`, defaultUsers);
  return defaultUsers;
}

/**
 * Gerar conteúdo da notificação baseado no contrato e tipo
 */
function generateNotificationContent(
  contract: ExpiringContract, 
  type: 'CONTRACT_EXPIRING' | 'CONTRACT_EXPIRED'
): { title: string; message: string } {
  
  const contractTypeNames: Record<string, string> = {
    'CERT_GAME': 'Certificado de Jogo',
    'CERT_RNG': 'Certificado RNG',
    'CERT_RGS': 'Certificado RGS',
    'CERT_PLATFORM': 'Certificado de Plataforma',
    'CERT_INTEGRATION': 'Certificado de Integração',
    'CERT_KYC': 'Certificado KYC',
    'CERT_PAYMENT': 'Certificado de Pagamento',
    'CERT_SPORTSBOOK': 'Certificado Sportsbook',
  };

  const contractTypeName = contractTypeNames[contract.contractType] || contract.contractType;
  const entityName = contract.entityName || 'Entidade desconhecida';

  if (type === 'CONTRACT_EXPIRED') {
    return {
      title: 'Contrato Expirado',
      message: `O ${contractTypeName} da empresa ${entityName} expirou em ${contract.expirationDate.toLocaleDateString('pt-BR')}.`,
    };
  } else {
    const days = contract.daysUntilExpiration;
    const dayText = days === 1 ? 'dia' : 'dias';
    
    return {
      title: `Contrato vencendo em ${days} ${dayText}`,
      message: `O ${contractTypeName} da empresa ${entityName} vence em ${days} ${dayText} (${contract.expirationDate.toLocaleDateString('pt-BR')}).`,
    };
  }
}

// Função principal para iniciar o worker
async function startNotificationWorker() {
  try {
    logger.info('Starting Notification Worker...');

    // Conectar às databases
    await connectDatabases();

    logger.info('Notification Worker started successfully');

    // Manter o processo vivo
    process.on('SIGINT', async () => {
      logger.info('Shutting down Notification Worker...');
      await notificationWorker.close();
      await redisConnection.quit();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('Shutting down Notification Worker...');
      await notificationWorker.close();
      await redisConnection.quit();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start Notification Worker:', error);
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  startNotificationWorker();
}

export { notificationWorker, startNotificationWorker };
