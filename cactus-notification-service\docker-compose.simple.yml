version: '3.8'

services:
  # PostgreSQL Database para notificações
  postgres:
    image: postgres:15-alpine
    container_name: cactus-notifications-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: cactus_notifications
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - cactus-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres -d cactus_notifications']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis para BullMQ
  redis:
    image: redis:7
    container_name: cactus-notifications-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - cactus-network

  # Cactus Notification Service API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cactus-notifications-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_URL: "********************************************/cactus_notifications?schema=notifications"
      BACKOFFICE_DATABASE_URL: ${BACKOFFICE_DATABASE_URL:-postgresql://backoffice_user:<EMAIL>:5433/backoffice_hml?schema=core}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CONTRACT_EXPIRATION_DAYS_AHEAD: "30,15,7,1"
      CONTRACT_CHECK_CRON: "0 * * * *"
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - cactus-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
    command: sh -c "npm run db:migrate && npm run db:seed && npm run dev"

  # Worker para leitura de contratos
  worker-contracts:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cactus-worker-contracts
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: "********************************************/cactus_notifications?schema=notifications"
      BACKOFFICE_DATABASE_URL: ${BACKOFFICE_DATABASE_URL:-postgresql://backoffice_user:<EMAIL>:5433/backoffice_hml?schema=core}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CONTRACT_EXPIRATION_DAYS_AHEAD: "30,15,7,1"
      CONTRACT_CHECK_CRON: "0 * * * *"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - cactus-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
    command: npm run worker:contracts

  # Worker para salvamento de notificações
  worker-notifications:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cactus-worker-notifications
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: "********************************************/cactus_notifications?schema=notifications"
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - cactus-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
    command: npm run worker:notifications

volumes:
  postgres_data:
    driver: local

networks:
  cactus-network:
    driver: bridge
