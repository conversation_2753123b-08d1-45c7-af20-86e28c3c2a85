import { Injectable } from "@nestjs/common";
import { IContractRepository } from "../../domain/repositories/contract.repository.interface";
import { PrismaService } from "@/infrastructure/prisma/prisma.service";
import { ContractStatus, DocumentStatus, Prisma } from "@prisma/client";
import { Contract } from "../../domain/entities/contract.entity";
import { EntityType } from "../../domain/enums/entity-type.enum";


@Injectable()
export class ContractRepository implements IContractRepository {
  constructor(private readonly prisma: PrismaService) { }
  updateStatusToArchived(uuid: string, archivedBy: string, updatedAt: Date): Promise<void> {
    throw new Error("Method not implemented.");
  }
  async updateStatusToRejected(
    uuid: string,
    archivedBy: string,
    updatedAt: Date,
  ): Promise<void> {
    await this.prisma.contract.update({
      where: { id: uuid },
      data: {
        status: ContractStatus.REJECTED,
        updatedBy: archivedBy,
        updatedAt,
      },
    });
  }
  async updateStatusToApproved(
    uuid: string,
    archivedBy: string,
    updatedAt: Date,
  ): Promise<void> {
    await this.prisma.contract.update({
      where: { id: uuid },
      data: {
        status: ContractStatus.APPROVED,
        updatedBy: archivedBy,
        updatedAt,
      },
    });
  }

  async create(contract: Contract): Promise<Contract> {
    const result = await this.prisma.$transaction(async (tx) => {
      const created = await tx.contract.create({
        data: {
          id: contract.uuid,
          entityType: contract.entityType,
          entityUuid: contract.entityUuid,
          currentVersion: contract.currentVersion,
          status: contract.status as ContractStatus,
          contractType: contract.contractType,
          createdBy: contract.createdBy,
          updatedBy: contract.createdBy,
          createdAt: contract.createdAt,
          updatedAt: contract.updatedAt,
          versions: {
            create: contract.versions?.map((v) => ({
              versionId: v.versionId,
              uploadedBy: v.uploadedBy,
              filePath: v.filePath,
              startDate: v.startDate,
              expirationDate: v.expirationDate,
              observations: v.observations,
              uploadedAt: v.uploadedAt,
              createdAt: v.uploadedAt,
            })),
          },
        },
        include: { versions: true },
      });

      return {
        uuid: created.id,
        entityType: created.entityType,
        entityUuid: created.entityUuid,
        currentVersion: created.currentVersion,
        status: created.status,
        contractType: created.contractType,
        createdBy: created.createdBy,
        uptadedBy: created.createdBy,
        createdAt: created.createdAt,
        updatedAt: created.updatedAt,
        versions: created.versions.map((v) => ({
          id: v.id,
          versionId: v.versionId,
          uploadedBy: v.uploadedBy,
          filePath: v.filePath,
          signed: v.signed,
          validatedBy: v.validatedBy,
          validatedAt: v.validatedAt,
          startDate: v.startDate,
          expirationDate: v.expirationDate,
          observations: v.observations,
          contractId: v.contractId,
          uploadedAt: v.uploadedAt,
          createdAt: v.createdAt,
        })),
      };
    });

    return result;
  }

  async findByUuid(uuid: string): Promise<Contract | null> {
    const result = await this.prisma.contract.findUnique({
      where: { id: uuid },
      include: { versions: true },
    });

    if (!result) return null;

    return {
      uuid: result.id,
      entityType: result.entityType,
      entityUuid: result.entityUuid,
      currentVersion: result.currentVersion,
      status: result.status,
      contractType: result.contractType,
      createdBy: result.createdBy,
      uptadedBy: result.createdBy,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      versions: result.versions.map((v) => ({
        id: v.id,
        versionId: v.versionId,
        uploadedBy: v.uploadedBy,
        filePath: v.filePath,
        signed: v.signed,
        validatedBy: v.validatedBy,
        validatedAt: v.validatedAt,
        startDate: v.startDate,
        expirationDate: v.expirationDate,
        observations: v.observations,
        contractId: v.contractId,
        uploadedAt: v.uploadedAt,
        createdAt: v.createdAt,
      })),
    };
  }

  async list(
    filters: Partial<Pick<Contract, 'status' | 'entityType' | 'entityUuid'>>,
    limit: number,
    offset: number,
  ): Promise<{ items: Contract[]; total: number }> {
    const where: Prisma.ContractWhereInput = {};

    if (filters.entityType)
      where.entityType = filters.entityType
    if (filters.status) where.status = filters.status
    if (filters.entityUuid) where.entityUuid = filters.entityUuid;

    const [itemsRaw, total] = await this.prisma.$transaction([
      this.prisma.contract.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: { updatedAt: 'desc' },
        include: { versions: true },
      }),
      this.prisma.contract.count({ where }),
    ]);

    const items: Contract[] = itemsRaw.map((d) => ({
      uuid: d.id,
      entityType: d.entityType,
      entityUuid: d.entityUuid,
      currentVersion: d.currentVersion,
      status: d.status as ContractStatus,
      contractType: d.contractType,
      createdBy: d.createdBy,
      uptadedBy: d.createdBy,
      createdAt: d.createdAt,
      updatedAt: d.updatedAt,
      versions: d.versions.map((v) => ({
        id: v.id,
        versionId: v.versionId,
        uploadedBy: v.uploadedBy,
        filePath: v.filePath,
        signed: v.signed,
        validatedBy: v.validatedBy,
        validatedAt: v.validatedAt,
        startDate: v.startDate,
        expirationDate: v.expirationDate,
        observations: v.observations,
        contractId: v.contractId,
        uploadedAt: v.uploadedAt,
        createdAt: v.createdAt,
      })),
    }));

    return { items, total };
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.contractVersion.deleteMany({
      where: { contractId: uuid }
    });
    await this.prisma.contract.delete({
      where: { id: uuid }
    });
  }

  async update(uuid: string, data: Partial<Contract>): Promise<Contract> {
    // Remove 'versions' do objeto data, se existir
    const { versions, ...dataWithoutVersions } = data;
    const updated = await this.prisma.contract.update({
      where: { id: uuid },
      data: {
        ...dataWithoutVersions,
        updatedAt: new Date(),
      },
      include: { versions: true },
    });
    return {
      uuid: updated.id,
      entityType: updated.entityType,
      entityUuid: updated.entityUuid,
      currentVersion: updated.currentVersion,
      status: updated.status,
      contractType: updated.contractType,
      createdBy: updated.createdBy,
      uptadedBy: updated.createdBy,
      createdAt: updated.createdAt,
      updatedAt: updated.updatedAt,
      versions: (updated.versions ?? []).map((v) => ({
        id: v.id,
        versionId: v.versionId,
        uploadedBy: v.uploadedBy,
        filePath: v.filePath,
        signed: v.signed,
        validatedBy: v.validatedBy,
        validatedAt: v.validatedAt,
        startDate: v.startDate,
        expirationDate: v.expirationDate,
        observations: v.observations,
        contractId: v.contractId,
        uploadedAt: v.uploadedAt,
        createdAt: v.createdAt,
      })),
    };
  }

  async createVersion(contractId: string, data: { uploadedBy: string; filePath: string; signed: boolean; startDate?: Date; expirationDate?: Date; observations?: string }): Promise<void> {
    // Busca o contrato para saber o próximo versionId
    const contract = await this.prisma.contract.findUnique({ where: { id: contractId }, include: { versions: true } });
    if (!contract) throw new Error('Contrato não encontrado');
    const nextVersionId = (contract.versions?.length ? Math.max(...contract.versions.map(v => v.versionId)) : 0) + 1;
    await this.prisma.$transaction([
      this.prisma.contractVersion.create({
        data: {
          contractId,
          versionId: nextVersionId,
          uploadedBy: data.uploadedBy,
          filePath: data.filePath,
          signed: data.signed,
          startDate: data.startDate,
          expirationDate: data.expirationDate,
          observations: data.observations,
        },
      }),
      this.prisma.contract.update({
        where: { id: contractId },
        data: { currentVersion: nextVersionId },
      }),
    ]);
  }
}
