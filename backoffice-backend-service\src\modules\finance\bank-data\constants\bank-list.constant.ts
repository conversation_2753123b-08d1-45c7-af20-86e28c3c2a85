export const BANK_LIST: Record<string, string> = {
  '1': 'Banco do Brasil S.A.',
  '3': 'Banco da Amazônia S.A.',
  '4': 'Banco do Nordeste do Brasil S.A.',
  '7': 'Banco Nacional de Desenvolvimento Econômico e Social - BNDES',
  '12': 'Banco Inbursa S.A.',
  '14': 'State Street Brasil S.A. - Banco Comercial',
  '17': 'BNY Mellon Banco S.A.',
  '18': 'Banco Tricury S.A.',
  '21': 'BANESTES S.A. Banco do Estado do Espírito Santo',
  '24': 'Banco BANDEPE S.A.',
  '25': 'Banco Alfa S.A.',
  '29': 'Banco Itaú Consignado S.A.',
  '33': 'Banco <PERSON>nder (Brasil) S.A.',
  '36': 'Banco Bradesco BBI S.A.',
  '37': 'Banco do Estado do Pará S.A.',
  '40': 'Banco Cargill S.A.',
  '41': 'Banco do Estado do Rio Grande do Sul S.A.',
  '47': 'Banco do Estado de Sergipe S.A.',
  '51': 'Banco de Desenvolvimento do Espírito Santo S.A.',
  '62': 'Hipercard Banco Múltiplo S.A.',
  '63': 'Banco Bradescard S.A.',
  '64': 'Goldman Sachs do Brasil Banco Múltiplo S.A.',
  '65': 'Banco Andbank (Brasil) S.A.',
  '66': 'Banco Morgan Stanley S.A.',
  '69': 'Banco Crefisa S.A.',
  '70': 'BRB - Banco de Brasília S.A.',
  '74': 'Banco J. Safra S.A.',
  '75': 'Banco ABN AMRO S.A.',
  '76': 'Banco KDB S.A.',
  '77': 'Banco Inter S.A.',
  '78': 'Haitong Banco de Investimento do Brasil S.A.',
  '79': 'Banco Original do Agronegócio S.A.',
  '81': 'BancoSeguro S.A.',
  '82': 'Banco Topázio S.A.',
  '83': 'Banco da China Brasil S.A.',
  '84': 'Uniprime Norte do Paraná - Coop de Economia e Crédito Mútuo dos Médicos, Profissionais das Ciências',
  '85': 'Cooperativa Central de Crédito - AILOS',
  '92': 'Brickell S.A. Crédito, Financiamento e Investimento',
  '94': 'Banco Finaxis S.A.',
  '95': 'Banco Travelex S.A.',
  '96': 'Banco B3 S.A.',
  '97': 'Cooperativa Central de Crédito Noroeste Brasileiro Ltda.',
  '104': 'Caixa Econômica Federal',
  '107': 'Banco BOCOM BBM S.A.',
  '118': 'Standard Chartered Bank (Brasil) S/A–Bco Invest.',
  '119': 'Banco Western Union do Brasil S.A.',
  '120': 'Banco Rodobens S.A.',
  '121': 'Banco Agibank S.A.',
  '122': 'Banco Bradesco BERJ S.A.',
  '124': 'Banco Woori Bank do Brasil S.A.',
  '125': 'Banco Genial S.A.',
  '126': 'BR Partners Banco de Investimento S.A.',
  '128': 'Braza Bank S.A. Banco de Câmbio',
  '129': 'UBS Brasil Banco de Investimento S.A.',
  '132': 'ICBC do Brasil Banco Múltiplo S.A.',
  '136':
    'Unicred do Brasil - Confederação Nacional das Cooperativas Centrais Unicred LTDA',
  '139': 'Intesa Sanpaolo Brasil S.A. - Banco Múltiplo',
  '144': 'Ebury Banco de Câmbio S.A.',
  '159': 'Casa do Crédito S.A. - Sociedade de Crédito ao Microoempreendedor',
  '163': 'Commerzbank Brasil S.A. - Banco Múltiplo',
  '184': 'Banco Itaú BBA S.A.',
  '204': 'Banco Bradesco Cartões S.A.',
  '208': 'Banco BTG Pactual S.A.',
  '212': 'Banco Original S.A.',
  '213': 'Banco Arbi S.A.',
  '217': 'Banco John Deere S.A.',
  '218': 'Banco BS2 S.A.',
  '222': 'Banco Credit Agricole Brasil S.A.',
  '224': 'Banco Fibra S.A.',
  '233': 'Banco Cifra S.A.',
  '237': 'Banco Bradesco S.A.',
  '241': 'Banco Clássico S.A.',
  '243': 'Banco Master S.A.',
  '246': 'Banco ABC Brasil S.A.',
  '249': 'Banco Investcred Unibanco S.A.',
  '250': 'BCV - Banco de Crédito e Varejo S.A.',
  '254': 'Paraná Banco S.A.',
  '265': 'Banco Fator S.A.',
  '266': 'Banco Cédula S.A.',
  '269': 'Banco HSBC S.A.',
  '276': 'Banco Senff S.A.',
  '299': 'Banco Afinz S.A. Banco Múltiplo',
  '300': 'Banco de La Nacion Argentina',
  '318': 'Banco BMG S.A.',
  '320': 'Bank Of China (Brasil) Banco Múltiplo S.A.',
  '329': 'QI SOCIEDADE DE CREDITO DIRETO S.A.',
  '330': 'Banco Bari de Investimentos e Financiamentos S/A',
  '341': 'Itaú Unibanco S.A.',
  '348': 'Banco XP S.A.',
  '359': 'Zema Credito, Financiamento e Investimento S.A.',
  '366': 'Banco Société Générale Brasil S.A.',
  '370': 'Banco Mizuho do Brasil S.A.',
  '373': 'UP.P Sociedade de Empréstimo Entre Pessoas S.A.',
  '376': 'Banco J. P. Morgan S.A.',
  '389': 'Banco Mercantil do Brasil S.A.',
  '394': 'Banco Bradesco Financiamentos S.A.',
  '399': 'Kirton Bank S.A. - Banco Múltiplo',
  '412': 'Social Bank Banco Múltiplo',
  '418': 'Zipidin Soluções Digitais Sociedade de Crédito Direto S.A.',
  '422': 'Banco Safra S.A.',
  '456': 'Banco MUFG Brasil S.A.',
  '464': 'Banco Sumitomo Mitsui Brasileiro S.A.',
  '473': 'Banco Caixa Geral - Brasil S.A.',
  '477': 'Citibank N.A.',
  '478': 'Gazincred S.A. Sociedade de Credito, Financiamento e Investimento',
  '479': 'Banco ItauBank S.A',
  '487': 'Deutsche Bank S.A. - Banco Alemão',
  '488': 'JPMorgan Chase Bank, National Association',
  '492': 'ING Bank N.V.',
  '494': 'Banco de La Republica Oriental del Uruguay',
  '495': 'Banco de La Provincia de Buenos Aires',
  '496': 'BBVA Brasil Banco de Investimento S.A.',
  '505': 'Banco Credit Suisse (Brasil) S.A.',
  '600': 'Banco Luso Brasileiro S.A.',
  '604': 'Banco Industrial do Brasil S.A.',
  '610': 'Banco VR S.A.',
  '611': 'Banco Paulista S.A.',
  '612': 'Banco Guanabara S.A.',
  '613': 'Omni Banco S.A.',
  '623': 'Banco PAN S.A.',
  '626': 'Banco C6 Consignado S.A.',
  '630': 'Banco Letsbank S.A.',
  '633': 'Banco Rendimento S.A.',
  '634': 'Banco Triângulo S.A.',
  '637': 'Banco Sofisa S.A.',
  '641': 'Banco Alvorada S.A.',
  '643': 'Banco Pine S.A.',
  '652': 'Itaú Unibanco Holding S.A.',
  '653': 'Banco Voiter S.A.',
  '654': 'Banco Digimais S.A.',
  '655': 'Banco Votorantim S.A.',
  '658': 'Banco Porto Real de Investimentos S.A.',
  '707': 'Banco Daycoval S.A.',
  '712': 'Banco Ourinvest S.A.',
  '720': 'BANCO RNX S.A',
  '739': 'Banco Cetelem S.A.',
  '741': 'Banco Ribeirão Preto S.A.',
  '743': 'Banco Semear S.A.',
  '745': 'Banco Citibank S.A.',
  '746': 'Banco Modal S.A.',
  '747': 'Banco Rabobank International Brasil S.A.',
  '748': 'Banco Cooperativo Sicredi S.A.',
  '751': 'Scotiabank Brasil S.A. Banco Múltiplo',
  '752': 'Banco BNP Paribas Brasil S.A.',
  '753': 'Novo Banco Continental S.A. - Banco Múltiplo',
  '754': 'Banco Sistema S.A.',
  '755': 'Bank of America Merrill Lynch Banco Múltiplo S.A.',
  '756': 'Banco Cooperativo Sicoob S.A.',
  '757': 'Banco KEB HANA do Brasil S.A.',
  '964': 'BECKER FINANCEIRA S.A CREDITO, FINANCIAMENTO E INVESTIMENTO',
  '087-6':
    'Cooperativa Central de Economia e Crédito Mútuo das Unicreds de Santa Catarina e Paraná',
  '089-2': 'Cooperativa de Crédito Rural da Região da Mogiana',
  '090-2': 'Cooperativa Central de Economia e Crédito Mutuo - SICOOB UNIMAIS',
  '091-4': 'Unicred Central do Rio Grande do Sul',
  '098-1': 'CREDIALIANÇA COOPERATIVA DE CRÉDITO RURAL',
  '114-7':
    'Central das Cooperativas de Economia e Crédito Mútuo do Estado do Espírito Santo Ltda.',
};
