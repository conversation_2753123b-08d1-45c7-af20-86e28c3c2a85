import { db } from '../database/connection';
import Redis from 'ioredis';
import { env } from '../config/env';
import logger from '../logger/logger';
import { metricsService } from '../metrics/metrics.service';

export interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  checks: {
    database: {
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    };
    redis: {
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    };
  };
}

export class HealthService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis({
      host: env.REDIS_HOST,
      port: env.REDIS_PORT,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      connectTimeout: 5000,
      commandTimeout: 5000,
    });
  }

  /**
   * Liveness probe - verifica se a aplicação está viva
   * Não deve fazer verificações de dependências externas
   */
  async isLive(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Readiness probe - verifica se a aplicação está pronta para receber tráfego
   * Verifica todas as dependências críticas
   */
  async isReady(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString();
    const checks = {
      database: await this.checkDatabase(),
      redis: await this.checkRedis(),
    };

    const allHealthy = Object.values(checks).every(
      (check) => check.status === 'up'
    );

    const healthStatus: HealthStatus = {
      status: allHealthy ? 'healthy' : 'unhealthy',
      timestamp,
      checks,
    };

    // Log do status de saúde
    if (allHealthy) {
      logger.info('Health check passed', { healthStatus });
    } else {
      logger.warn('Health check failed', { healthStatus });
    }

    return healthStatus;
  }

  /**
   * Verifica a conexão com o banco de dados PostgreSQL
   */
  private async checkDatabase(): Promise<{
    status: 'up' | 'down';
    responseTime?: number;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      // Executa uma query simples para testar a conexão
      await db.execute('SELECT 1 as health_check');

      const responseTime = Date.now() - startTime;

      // Registra métrica de sucesso
      metricsService.recordDatabaseConnection('success');

      return {
        status: 'up',
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown database error';

      // Registra métrica de erro
      metricsService.recordDatabaseConnection('error');

      logger.error('Database health check failed', {
        error: errorMessage,
        responseTime,
      });

      return {
        status: 'down',
        responseTime,
        error: errorMessage,
      };
    }
  }

  /**
   * Verifica a conexão com o Redis
   */
  private async checkRedis(): Promise<{
    status: 'up' | 'down';
    responseTime?: number;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      // Testa a conexão com um ping
      await this.redis.ping();

      const responseTime = Date.now() - startTime;

      // Registra métrica de sucesso
      metricsService.recordRedisConnection('success');

      return {
        status: 'up',
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown Redis error';

      // Registra métrica de erro
      metricsService.recordRedisConnection('error');

      logger.error('Redis health check failed', {
        error: errorMessage,
        responseTime,
      });

      return {
        status: 'down',
        responseTime,
        error: errorMessage,
      };
    }
  }

  /**
   * Fecha as conexões (útil para testes e shutdown)
   */
  async close(): Promise<void> {
    try {
      await this.redis.quit();
    } catch (error) {
      logger.error('Error closing Redis connection', { error });
    }
  }
}

// Instância singleton do serviço de health
export const healthService = new HealthService();
