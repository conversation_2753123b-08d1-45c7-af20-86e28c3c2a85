-- CreateEnum
CREATE TYPE "finance"."BankAccountType" AS ENUM ('CHECKING', 'SAVINGS', 'SALARY', 'BUSINESS');

-- CreateEnum
CREATE TYPE "finance"."BankPixKeyType" AS ENUM ('CPF', '<PERSON>NPJ', 'EMAIL', 'PHON<PERSON>', '<PERSON><PERSON><PERSON>');

-- CreateEnum
CREATE TYPE "finance"."BankDataStatus" AS ENUM ('ACTIVE', 'AWAITING_APPROVAL', 'REJECTED', 'INACTIVE');

-- CreateTable
CREATE TABLE "finance"."bank_data" (
    "id" TEXT NOT NULL,
    "entityType" "core"."EntityType" NOT NULL,
    "entityUuid" TEXT NOT NULL,
    "bankName" VARCHAR(100) NOT NULL,
    "bankCode" VARCHAR(10) NOT NULL,
    "accountType" "finance"."BankAccountType" NOT NULL,
    "agency_number" VARCHAR(10) NOT NULL,
    "agency_digit" VARCHAR(5),
    "account_number" VARCHAR(10) NOT NULL,
    "account_digit" VARCHAR(5) NOT NULL,
    "account_holder_name" VARCHAR(100) NOT NULL,
    "account_holder_document" CHAR(14) NOT NULL,
    "pix_key" VARCHAR(100),
    "pixKeyType" "finance"."BankPixKeyType",
    "is_digital_bank" BOOLEAN DEFAULT false,
    "status" "finance"."BankDataStatus",
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "bank_data_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "finance"."bank_account_update_data" (
    "id" TEXT NOT NULL,
    "bank_data_id" TEXT NOT NULL,
    "field_name" VARCHAR(100) NOT NULL,
    "old_value" VARCHAR(255),
    "new_value" VARCHAR(255),
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "entityType" "core"."EntityType" NOT NULL,
    "entityUuid" TEXT NOT NULL,

    CONSTRAINT "bank_account_update_data_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "bank_data_entityUuid_key" ON "finance"."bank_data"("entityUuid");

-- CreateIndex
CREATE INDEX "bank_data_entityType_entityUuid_idx" ON "finance"."bank_data"("entityType", "entityUuid");

-- CreateIndex
CREATE INDEX "bank_account_update_data_bank_data_id_idx" ON "finance"."bank_account_update_data"("bank_data_id");

-- AddForeignKey
ALTER TABLE "finance"."bank_account_update_data" ADD CONSTRAINT "bank_account_update_data_bank_data_id_fkey" FOREIGN KEY ("bank_data_id") REFERENCES "finance"."bank_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
