import { SupplierType } from './supplier-type.enum';

describe('SupplierType Enum', () => {
  it('should have the correct values', () => {
    expect(SupplierType.BANK).toBe('BANK');
    expect(SupplierType.GAME).toBe('GAME');
    expect(SupplierType.SPORTSBOOK).toBe('SPORTSBOOK');
    expect(SupplierType.KYC).toBe('KYC');
    expect(SupplierType.OTHER).toBe('OTHER');
  });

  it('should have exactly five status options', () => {
    const statusValues = Object.values(SupplierType);
    expect(statusValues).toHaveLength(5);
    expect(statusValues).toContain('BANK');
    expect(statusValues).toContain('GAME');
    expect(statusValues).toContain('SPORTSBOOK');
    expect(statusValues).toContain('KYC');
    expect(statusValues).toContain('OTHER');
  });

  it('should have the correct keys', () => {
    const statusKeys = Object.keys(SupplierType);
    expect(statusKeys).toHaveLength(5);
    expect(statusKeys).toContain('BANK');
    expect(statusKeys).toContain('GAME');
    expect(statusKeys).toContain('SPORTSBOOK');
    expect(statusKeys).toContain('KYC');
    expect(statusKeys).toContain('OTHER');
  });

  it('should not allow invalid status values', () => {
    const validStatuses = Object.values(SupplierType);
    const invalidStatus = 'clothing' as SupplierType;

    expect(validStatuses).not.toContain(invalidStatus);
  });
});
