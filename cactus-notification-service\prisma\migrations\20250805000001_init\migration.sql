-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "notifications";

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "notifications"."NotificationType" AS ENUM ('CONTRACT_EXPIRING', 'CONTRACT_EXPIRED', 'DOCUMENT_EXPIRING', 'DOCUMENT_EXPIRED', 'SYSTEM_ALERT');

-- CreateTable
CREATE TABLE "notifications"."notifications" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "type" "notifications"."NotificationType" NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "data" JSONB,
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "read_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notifications"."user_notification_settings" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "contract_expiration" BOOLEAN NOT NULL DEFAULT true,
    "document_expiration" BOOLEAN NOT NULL DEFAULT true,
    "email_notifications" BOOLEAN NOT NULL DEFAULT true,
    "push_notifications" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_notification_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_notification_settings_user_id_key" ON "notifications"."user_notification_settings"("user_id");

-- CreateIndex
CREATE INDEX "notifications_user_id_idx" ON "notifications"."notifications"("user_id");

-- CreateIndex
CREATE INDEX "notifications_type_idx" ON "notifications"."notifications"("type");

-- CreateIndex
CREATE INDEX "notifications_created_at_idx" ON "notifications"."notifications"("created_at");

-- CreateIndex
CREATE INDEX "notifications_is_read_idx" ON "notifications"."notifications"("is_read");
