import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { BankDataResponseDto } from './bank-data-response.dto';
import {
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

describe('BankDataResponseDto', () => {
  describe('Valid data', () => {
    it('should validate with all required fields', async () => {
      const dto = plainToClass(BankDataResponseDto, {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: '<PERSON>',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: false,
        status: BankDataStatus.ACTIVE,
        employeeId: 1,
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T00:00:00.000Z'),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal required fields', async () => {
      const dto = plainToClass(BankDataResponseDto, {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.SAVINGS,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with null optional fields', async () => {
      const dto = plainToClass(BankDataResponseDto, {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: null,
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
        pixKey: null,
        pixKeyType: null,
        isDigitalBank: null,
        status: null,
        employeeId: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with different account types', async () => {
      const accountTypes = [BankAccountType.CHECKING, BankAccountType.SAVINGS];

      for (const accountType of accountTypes) {
        const dto = plainToClass(BankDataResponseDto, {
          id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
          bankName: 'Banco do Brasil S.A.',
          bankCode: '001',
          accountType,
          agencyNumber: '1234',
          accountNumber: '123456',
          accountDigit: '7',
          accountHolderName: 'João da Silva',
          accountHolderDocument: '***********',
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate with different PIX key types', async () => {
      const pixKeyTypes = [
        BankPixKeyType.EMAIL,
        BankPixKeyType.PHONE,
        BankPixKeyType.CPF,
        BankPixKeyType.CNPJ,
        BankPixKeyType.RANDOM,
      ];

      for (const pixKeyType of pixKeyTypes) {
        const dto = plainToClass(BankDataResponseDto, {
          id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
          bankName: 'Banco do Brasil S.A.',
          bankCode: '001',
          accountType: BankAccountType.CHECKING,
          agencyNumber: '1234',
          accountNumber: '123456',
          accountDigit: '7',
          accountHolderName: 'João da Silva',
          accountHolderDocument: '***********',
          pixKey: '<EMAIL>',
          pixKeyType,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate with different status values', async () => {
      const statuses = [BankDataStatus.ACTIVE, BankDataStatus.INACTIVE];

      for (const status of statuses) {
        const dto = plainToClass(BankDataResponseDto, {
          id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
          bankName: 'Banco do Brasil S.A.',
          bankCode: '001',
          accountType: BankAccountType.CHECKING,
          agencyNumber: '1234',
          accountNumber: '123456',
          accountDigit: '7',
          accountHolderName: 'João da Silva',
          accountHolderDocument: '***********',
          status,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });
  });

  describe('Data transformation', () => {
    it('should preserve all field values correctly', () => {
      const inputData = {
        id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: true,
        status: BankDataStatus.ACTIVE,
        employeeId: 123,
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T00:00:00.000Z'),
      };

      const dto = plainToClass(BankDataResponseDto, inputData);

      expect(dto.id).toBe(inputData.id);
      expect(dto.bankName).toBe(inputData.bankName);
      expect(dto.bankCode).toBe(inputData.bankCode);
      expect(dto.accountType).toBe(inputData.accountType);
      expect(dto.agencyNumber).toBe(inputData.agencyNumber);
      expect(dto.agencyDigit).toBe(inputData.agencyDigit);
      expect(dto.accountNumber).toBe(inputData.accountNumber);
      expect(dto.accountDigit).toBe(inputData.accountDigit);
      expect(dto.accountHolderName).toBe(inputData.accountHolderName);
      expect(dto.accountHolderDocument).toBe(inputData.accountHolderDocument);
      expect(dto.pixKey).toBe(inputData.pixKey);
      expect(dto.pixKeyType).toBe(inputData.pixKeyType);
      expect(dto.isDigitalBank).toBe(inputData.isDigitalBank);
      expect(dto.status).toBe(inputData.status);
      expect(dto.employeeId).toBe(inputData.employeeId);
      expect(dto.createdAt).toEqual(inputData.createdAt);
      expect(dto.updatedAt).toEqual(inputData.updatedAt);
    });

    it('should handle boolean values correctly', () => {
      const dtoTrue = plainToClass(BankDataResponseDto, {
        id: 'test-id',
        bankName: 'Test Bank',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'Test User',
        accountHolderDocument: '***********',
        isDigitalBank: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const dtoFalse = plainToClass(BankDataResponseDto, {
        id: 'test-id',
        bankName: 'Test Bank',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'Test User',
        accountHolderDocument: '***********',
        isDigitalBank: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      expect(dtoTrue.isDigitalBank).toBe(true);
      expect(dtoFalse.isDigitalBank).toBe(false);
    });
  });
});
