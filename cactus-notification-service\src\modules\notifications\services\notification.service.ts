import { prismaLocal, notifyNewNotification } from '../../../shared/database/prisma';
import { logger } from '../../../shared/logger/logger';
import { 
  CreateNotificationDto, 
  UpdateNotificationDto, 
  NotificationFilters,
  UserNotificationSettingsDto 
} from '../../../shared/types/notification.types';
import { Notification, UserNotificationSettings, NotificationType } from '@prisma/client';

export class NotificationService {
  /**
   * Criar uma nova notificação
   */
  async createNotification(data: CreateNotificationDto): Promise<Notification> {
    try {
      logger.info(`Creating notification for user ${data.userId}`, { type: data.type, title: data.title });

      // Verificar se o usuário tem as notificações habilitadas
      const userSettings = await this.getUserSettings(data.userId);
      
      if (!this.shouldCreateNotification(data.type, userSettings)) {
        logger.info(`Notification skipped due to user settings`, { userId: data.userId, type: data.type });
        throw new Error('User has disabled this type of notification');
      }

      // Verificar se já existe uma notificação similar recente (evitar duplicatas)
      const existingNotification = await this.findSimilarRecentNotification(data);
      if (existingNotification) {
        logger.info(`Similar notification already exists`, { existingId: existingNotification.id });
        return existingNotification;
      }

      // Criar a notificação
      const notification = await prismaLocal.notification.create({
        data: {
          userId: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data || {},
        },
      });

      // Emitir NOTIFY no PostgreSQL
      await notifyNewNotification(notification.id);

      logger.info(`Notification created successfully`, { id: notification.id });
      return notification;

    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Buscar notificações com filtros
   */
  async findNotifications(filters: NotificationFilters): Promise<Notification[]> {
    try {
      const where: any = {};

      if (filters.userId) where.userId = filters.userId;
      if (filters.type) where.type = filters.type;
      if (filters.isRead !== undefined) where.isRead = filters.isRead;
      
      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) where.createdAt.gte = filters.startDate;
        if (filters.endDate) where.createdAt.lte = filters.endDate;
      }

      const notifications = await prismaLocal.notification.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: filters.limit || 50,
        skip: filters.offset || 0,
      });

      return notifications;

    } catch (error) {
      logger.error('Error finding notifications:', error);
      throw error;
    }
  }

  /**
   * Marcar notificação como lida
   */
  async markAsRead(notificationId: string, userId: string): Promise<Notification> {
    try {
      const notification = await prismaLocal.notification.update({
        where: { 
          id: notificationId,
          userId: userId, // Garantir que o usuário só pode marcar suas próprias notificações
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      logger.info(`Notification marked as read`, { id: notificationId, userId });
      return notification;

    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Marcar múltiplas notificações como lidas
   */
  async markMultipleAsRead(notificationIds: string[], userId: string): Promise<number> {
    try {
      const result = await prismaLocal.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: userId,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      logger.info(`${result.count} notifications marked as read`, { userId });
      return result.count;

    } catch (error) {
      logger.error('Error marking multiple notifications as read:', error);
      throw error;
    }
  }

  /**
   * Buscar configurações de notificação do usuário
   */
  async getUserSettings(userId: string): Promise<UserNotificationSettings> {
    try {
      let settings = await prismaLocal.userNotificationSettings.findUnique({
        where: { userId },
      });

      // Se não existir, criar com configurações padrão
      if (!settings) {
        settings = await prismaLocal.userNotificationSettings.create({
          data: {
            userId,
            contractExpiration: true,
            documentExpiration: true,
            emailNotifications: true,
            pushNotifications: true,
          },
        });
      }

      return settings;

    } catch (error) {
      logger.error('Error getting user settings:', error);
      throw error;
    }
  }

  /**
   * Atualizar configurações de notificação do usuário
   */
  async updateUserSettings(data: UserNotificationSettingsDto): Promise<UserNotificationSettings> {
    try {
      const settings = await prismaLocal.userNotificationSettings.upsert({
        where: { userId: data.userId },
        update: {
          contractExpiration: data.contractExpiration,
          documentExpiration: data.documentExpiration,
          emailNotifications: data.emailNotifications,
          pushNotifications: data.pushNotifications,
        },
        create: {
          userId: data.userId,
          contractExpiration: data.contractExpiration ?? true,
          documentExpiration: data.documentExpiration ?? true,
          emailNotifications: data.emailNotifications ?? true,
          pushNotifications: data.pushNotifications ?? true,
        },
      });

      logger.info(`User notification settings updated`, { userId: data.userId });
      return settings;

    } catch (error) {
      logger.error('Error updating user settings:', error);
      throw error;
    }
  }

  /**
   * Contar notificações não lidas do usuário
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const count = await prismaLocal.notification.count({
        where: {
          userId,
          isRead: false,
        },
      });

      return count;

    } catch (error) {
      logger.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * Verificar se deve criar a notificação baseado nas configurações do usuário
   */
  private shouldCreateNotification(type: NotificationType, settings: UserNotificationSettings): boolean {
    switch (type) {
      case 'CONTRACT_EXPIRING':
      case 'CONTRACT_EXPIRED':
        return settings.contractExpiration;
      case 'DOCUMENT_EXPIRING':
      case 'DOCUMENT_EXPIRED':
        return settings.documentExpiration;
      case 'SYSTEM_ALERT':
        return true; // Alertas do sistema sempre são criados
      default:
        return true;
    }
  }

  /**
   * Buscar notificação similar recente para evitar duplicatas
   */
  private async findSimilarRecentNotification(data: CreateNotificationDto): Promise<Notification | null> {
    try {
      // Buscar notificações similares nas últimas 24 horas
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const contractId = data.data?.contractId;
      if (!contractId) return null;

      const similar = await prismaLocal.notification.findFirst({
        where: {
          userId: data.userId,
          type: data.type,
          createdAt: { gte: oneDayAgo },
          data: {
            path: ['contractId'],
            equals: contractId,
          },
        },
      });

      return similar;

    } catch (error) {
      logger.warn('Error checking for similar notifications:', error);
      return null;
    }
  }
}
