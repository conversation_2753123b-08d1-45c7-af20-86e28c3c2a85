import { PrismaClient } from '@prisma/client';
import { logger } from '../logger/logger';

// Cliente Prisma para database local de notificações
export const prismaLocal = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
});

// Cliente Prisma para database do backoffice (read-only)
export const prismaBackoffice = new PrismaClient({
  datasources: {
    db: {
      url: process.env.BACKOFFICE_DATABASE_URL,
    },
  },
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
});

// Event listeners para logs
prismaLocal.$on('query', (e) => {
  logger.debug('Local DB Query: ' + e.query);
  logger.debug('Local DB Params: ' + e.params);
  logger.debug('Local DB Duration: ' + e.duration + 'ms');
});

prismaLocal.$on('error', (e) => {
  logger.error('Local DB Error: ' + e.message);
});

prismaBackoffice.$on('query', (e) => {
  logger.debug('Backoffice DB Query: ' + e.query);
  logger.debug('Backoffice DB Params: ' + e.params);
  logger.debug('Backoffice DB Duration: ' + e.duration + 'ms');
});

prismaBackoffice.$on('error', (e) => {
  logger.error('Backoffice DB Error: ' + e.message);
});

// Função para conectar ambas as databases
export async function connectDatabases() {
  try {
    await prismaLocal.$connect();
    logger.info('Connected to local notifications database');
    
    await prismaBackoffice.$connect();
    logger.info('Connected to backoffice database');
  } catch (error) {
    logger.error('Failed to connect to databases:', error);
    throw error;
  }
}

// Função para desconectar ambas as databases
export async function disconnectDatabases() {
  try {
    await prismaLocal.$disconnect();
    logger.info('Disconnected from local notifications database');
    
    await prismaBackoffice.$disconnect();
    logger.info('Disconnected from backoffice database');
  } catch (error) {
    logger.error('Failed to disconnect from databases:', error);
    throw error;
  }
}

// Função para configurar LISTEN/NOTIFY no PostgreSQL
export async function setupNotificationListener() {
  try {
    // Configurar LISTEN para notificações
    await prismaLocal.$executeRaw`LISTEN new_notification`;
    logger.info('PostgreSQL LISTEN configured for new_notification channel');
  } catch (error) {
    logger.error('Failed to setup notification listener:', error);
    throw error;
  }
}

// Função para emitir NOTIFY
export async function notifyNewNotification(notificationId: string) {
  try {
    await prismaLocal.$executeRaw`SELECT pg_notify('new_notification', ${notificationId})`;
    logger.debug(`Emitted NOTIFY for notification: ${notificationId}`);
  } catch (error) {
    logger.error('Failed to emit notification:', error);
    throw error;
  }
}
