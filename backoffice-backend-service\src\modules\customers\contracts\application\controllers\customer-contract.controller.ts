import { Controller, Post, Get, Patch, Delete, Param, Body, HttpCode, HttpStatus, UploadedFile, UseInterceptors, UploadedFiles, Request, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { ContractDto, CreateContractDto } from '../../infrastructure/dtos/customercontract.dto';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../../auth/guards/roles.guard';
import { Roles } from '../../../../auth/decorators/roles.decorator';
import { Role } from '../../../../../core/domain/role.enum';
import { CustomerContractService } from '../services/customer-contract.service';
import { CreateContractsDto, ContractApiBodyDto, ContractTextFormDataDto, ContractSignPatchDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { ContractResponseDto } from '@/modules/documents/infrastructure/dto/contract-response.dto';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    [key: string]: any;
  };
}

@ApiTags('Customer Contracts')
@ApiBearerAuth()
@Controller('core/customers/:uuid/contracts')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CustomerContractController {
  constructor(
    private readonly customerContractService: CustomerContractService,
  ) { }

  @Post()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiOperation({
    summary: 'Criar contratos para o cliente e fazer upload dos arquivos.',
  })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Cliente',
    type: String,
  })
  @ApiBody({
    description:
      "Dados para criação de contratos. 'files' são os arquivos e 'contractsMetadata' é uma string JSON com os metadados.",
    type: ContractApiBodyDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Contratos criados com sucesso.',
    type: Array,
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiResponse({ status: 403, description: 'Acesso negado.' })
  async createContracts(
    @Param('uuid') customerUuid: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() formData: ContractTextFormDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<ContractResponseDto[]> {
    if (!files || files.length === 0) {
      throw new Error('Pelo menos um arquivo deve ser enviado.');
    }

    let contractsMetadataArray: unknown;
    try {
      contractsMetadataArray = JSON.parse(formData.contractsMetadata);
    } catch (_error) {
      throw new Error(
        'O campo contractsMetadata não é uma string JSON válida.',
      );
    }

    if (
      !Array.isArray(contractsMetadataArray) ||
      files.length !== contractsMetadataArray.length
    ) {
      throw new Error(
        'O número de metadatos de contrato não corresponde ao número de arquivos enviados.',
      );
    }

    const processedContractsData = (contractsMetadataArray as unknown[]).map((meta) => ({
      ...(meta as Record<string, unknown>),
      entityUuid: customerUuid,
      uploadedBy: req.user.id,
    }));

    const createContractsDto: CreateContractsDto = {
      contracts: processedContractsData as never,
    };

    const contracts = await this.customerContractService.createCustomerContract(
      customerUuid,
      files,
      req.user.id,
      createContractsDto,
    );

    return contracts.map(contract => ContractResponseDto.fromEntity(contract));
  }

  @Post('legacy')
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Criar contrato para o customer (método legado)' })
  @ApiResponse({ status: 201, type: ContractDto })
  @ApiBody({ type: CreateContractDto })
  async createContract(
    @Param('uuid') customerUuid: string,
    @Body() dto: any, // não validar multipart
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ContractDto> {
    // Este método legado pode ser mantido para compatibilidade
    // mas deveria ser migrado para usar o novo padrão
    throw new Error('Método legado não suportado. Use o endpoint principal.');
  }

  @Get()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({ summary: 'Listar contratos do customer' })
  @ApiResponse({ status: 200, type: [ContractResponseDto] })
  async getContracts(
    @Param('uuid') customerUuid: string,
  ): Promise<ContractResponseDto[]> {
    const contracts = await this.customerContractService.listCustomerContracts(customerUuid);
    return contracts.map(contract => ContractResponseDto.fromEntity(contract));
  }

  @Get(':contractUuid')
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({ summary: 'Buscar contrato específico do customer' })
  @ApiResponse({ status: 200, type: ContractResponseDto })
  async getContract(
    @Param('uuid') customerUuid: string,
    @Param('contractUuid') contractUuid: string,
  ): Promise<ContractResponseDto> {
    const contract = await this.customerContractService.getCustomerContract(customerUuid, contractUuid);
    if (!contract) throw new Error('Contract not found');
    return ContractResponseDto.fromEntity(contract);
  }

  @Patch(':contractUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Assinar ou rejeitar contrato do cliente' })
  @ApiParam({ name: 'uuid', required: true, description: 'UUID do Cliente', type: String })
  @ApiParam({ name: 'contractUuid', required: true, description: 'UUID do Contrato', type: String })
  @ApiBody({ description: 'Campos para assinatura/rejeição do contrato', type: ContractSignPatchDto })
  @ApiResponse({ status: 200, description: 'Contrato atualizado com sucesso.', type: ContractResponseDto })
  @ApiResponse({ status: 404, description: 'Contrato não encontrado para este cliente.' })
  async patchContract(
    @Param('uuid') customerUuid: string,
    @Param('contractUuid') contractUuid: string,
    @Body() patchDto: ContractSignPatchDto,
  ): Promise<ContractResponseDto> {
    const contract = await this.customerContractService.updateCustomerContract(customerUuid, contractUuid, patchDto);
    return ContractResponseDto.fromEntity(contract);
  }

  @Delete(':contractUuid')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover contrato do customer' })
  @ApiResponse({ status: 204 })
  async deleteContract(
    @Param('uuid') customerUuid: string,
    @Param('contractUuid') contractUuid: string,
  ): Promise<void> {
    await this.customerContractService.deleteCustomerContract(customerUuid, contractUuid);
  }
} 