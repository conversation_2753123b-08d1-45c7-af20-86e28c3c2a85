import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { Certificate } from '../../domain/entities/certificate.entity';
import { CreateCertificateDto } from '../../infrastructure/dtos/create-certificate.dto';
import { FindCustomerByUuidUseCase } from './find-customer-by-uuid.use-case';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';

interface CreateCertificateUseCaseRequest {
  customerUuid: string;
  uploadedById: string;
  file: Express.Multer.File;
  data: CreateCertificateDto;
}

@Injectable()
export class CreateCertificateUseCase {
  constructor(
    @Inject(CERTIFICATE_REPOSITORY)
    private readonly certificateRepository: ICertificateRepository,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
    private readonly findCustomerByUuidUseCase: FindCustomerByUuidUseCase,
    @Inject('UserRepository')
    private readonly userRepository: UserRepository,
  ) {}

  async execute({
    customerUuid,
    uploadedById,
    file,
    data,
  }: CreateCertificateUseCaseRequest): Promise<Certificate> {
    const customer = await this.findCustomerByUuidUseCase.execute(customerUuid);
    if (!customer) {
      throw new NotFoundException('Cliente não encontrado.');
    }

    const user = await this.userRepository.findByKeycloakId(uploadedById);

    const path = `customers/${customer.uuid}/certificates/${Date.now()}-${
      file.originalname
    }`;

    await this.storageProvider.upload(file.buffer, file.mimetype, path);

    if (!customer.id) {
      throw new Error('ID do cliente não encontrado');
    }

    const certificate = Certificate.create({
      customerId: customer.id,
      category: data.category,
      type: data.type,
      fileUrl: path,
      notes: data.notes,
      emissionDate: data.emissionDate ? new Date(data.emissionDate) : undefined,
      expirationDate: data.expirationDate
        ? new Date(data.expirationDate)
        : undefined,
      uploadedById: user.id,
      uploadedByName: user.name,
    });

    return this.certificateRepository.create(certificate);
  }
}
