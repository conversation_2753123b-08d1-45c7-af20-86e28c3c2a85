import Fastify from 'fastify';
import { env, isDevelopment } from './shared/config/env';
import { errorHandler } from './shared/middleware/error-handler.middleware';
import {
  connectDatabases,
  disconnectDatabases,
} from './shared/database/prisma';
import { NotificationListenerService } from './modules/notifications/services/notification-listener.service';
import { notificationRoutes } from './modules/notifications/routes/notification.routes';
import { logger } from './shared/logger/logger';
import { securityMiddleware } from './shared/middleware/security.middleware';
import healthPlugin from './shared/health/health.plugin';

// Instância global do listener de notificações
const notificationListener = new NotificationListenerService();

const fastify = Fastify({
  logger: isDevelopment
    ? {
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
          },
        },
      }
    : true,
});

// Middleware de segurança
fastify.addHook(
  'preHandler',
  securityMiddleware({
    enableSqlInjectionProtection: true,
    enableXssProtection: true,
    enablePathTraversalProtection: true,
    enableInputSanitization: true,
    strictMode: false,
    logAttempts: true,
  })
);

// Error handler
fastify.setErrorHandler(errorHandler);

const setupRoutes = async () => {
  // Registrar plugin de health checks
  await fastify.register(healthPlugin);

  // CORS
  if (env.CORS_ENABLED) {
    await fastify.register(import('@fastify/cors'), {
      origin: isDevelopment ? true : false,
    });
  }

  // Swagger para documentação da API
  if (isDevelopment) {
    await fastify.register(import('@fastify/swagger'), {
      swagger: {
        info: {
          title: 'Cactus Notification Service API',
          description: 'Sistema de notificações para contratos vencendo',
          version: '1.0.0',
        },
        host: `localhost:${env.PORT}`,
        schemes: ['http'],
        consumes: ['application/json'],
        produces: ['application/json', 'text/event-stream'],
        tags: [
          { name: 'notifications', description: 'Operações de notificações' },
          { name: 'admin', description: 'Operações administrativas' },
          { name: 'health', description: 'Health checks' },
        ],
      },
    });

    await fastify.register(import('@fastify/swagger-ui'), {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'full',
        deepLinking: false,
      },
    });
  }

  // Registrar rotas de notificações
  await fastify.register(
    async (fastify) => {
      await notificationRoutes(fastify, notificationListener);
    },
    { prefix: '/api' }
  );
};

const start = async () => {
  try {
    logger.info('Starting Cactus Notification Service...');

    // Conectar às databases
    await connectDatabases();

    // Iniciar o listener de notificações
    await notificationListener.startListener();

    // Configurar rotas
    await setupRoutes();

    // Iniciar servidor
    await fastify.listen({
      host: '0.0.0.0',
      port: env.PORT,
    });

    logger.info(
      `🚀 Cactus Notification Service started on http://localhost:${env.PORT}`
    );
    logger.info(`📖 API Documentation: http://localhost:${env.PORT}/docs`);
    logger.info(
      `🔄 SSE Endpoint: http://localhost:${env.PORT}/api/notifications/stream`
    );
    logger.info(`Environment: ${env.NODE_ENV}`);
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Graceful shutdown
const shutdown = async () => {
  try {
    logger.info('Shutting down Cactus Notification Service...');

    await notificationListener.stopListener();
    await fastify.close();
    await disconnectDatabases();

    logger.info('Server shutdown complete');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

start();
