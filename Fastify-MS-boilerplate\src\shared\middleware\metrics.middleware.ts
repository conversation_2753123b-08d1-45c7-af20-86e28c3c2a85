import { FastifyRequest, FastifyReply } from 'fastify';
import { metricsService } from '../metrics/metrics.service';
import logger from '../logger/logger';

export interface MetricsMiddlewareOptions {
  enableDetailedLogging?: boolean;
  excludePaths?: string[];
  slowRequestThreshold?: number; // em milissegundos
}

/**
 * Middleware para capturar métricas detalhadas das requisições
 */
export function createMetricsMiddleware(
  options: MetricsMiddlewareOptions = {}
) {
  const {
    enableDetailedLogging = false,
    excludePaths = ['/metrics', '/health/live'],
    slowRequestThreshold = 1000,
  } = options;

  return {
    // Hook executado antes da requisição
    onRequest: async (request: FastifyRequest, reply: FastifyReply) => {
      // Pula paths excluídos
      if (excludePaths.includes(request.url)) {
        return;
      }

      // Adiciona informações de timing
      (request as any).metricsData = {
        startTime: Date.now(),
        startHrTime: process.hrtime.bigint(),
      };

      if (enableDetailedLogging) {
        logger.info('Request started', {
          method: request.method,
          path: request.url,
          userAgent: request.headers['user-agent'] as string,
          ip: request.ip,
          requestId: request.id,
        });
      }
    },

    // Hook executado após a resposta
    onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
      const metricsData = (request as any).metricsData;

      if (!metricsData || excludePaths.includes(request.url)) {
        return;
      }

      const endTime = Date.now();
      const endHrTime = process.hrtime.bigint();

      const duration = (endTime - metricsData.startTime) / 1000; // em segundos
      const precisionDuration =
        Number(endHrTime - metricsData.startHrTime) / 1e9; // em segundos com precisão

      // Registra métricas
      metricsService.recordRequest(
        request.method,
        request.url,
        reply.statusCode,
        precisionDuration
      );

      // Log de requisições lentas
      if (endTime - metricsData.startTime > slowRequestThreshold) {
        logger.warn('Slow request detected', {
          method: request.method,
          path: request.url,
          statusCode: reply.statusCode,
          duration: endTime - metricsData.startTime,
          userAgent: request.headers['user-agent'] as string,
          ip: request.ip,
          requestId: request.id,
        });
      }

      if (enableDetailedLogging) {
        logger.logRequest({
          method: request.method,
          path: request.url,
          statusCode: reply.statusCode,
          duration: endTime - metricsData.startTime,
          requestId: request.id,
          userAgent: request.headers['user-agent'] as string,
          ip: request.ip,
        });
      }
    },

    // Hook executado em caso de erro
    onError: async (
      request: FastifyRequest,
      reply: FastifyReply,
      error: Error
    ) => {
      const metricsData = (request as any).metricsData;

      if (!metricsData || excludePaths.includes(request.url)) {
        return;
      }

      logger.logError(error, {
        method: request.method,
        path: request.url,
        userAgent: request.headers['user-agent'] as string,
        ip: request.ip,
        requestId: request.id,
      });

      // As métricas de erro serão capturadas no onResponse
    },
  };
}

/**
 * Middleware padrão com configurações otimizadas para produção
 */
export const defaultMetricsMiddleware = createMetricsMiddleware({
  enableDetailedLogging: process.env.NODE_ENV === 'development',
  excludePaths: ['/metrics', '/health/live', '/health/ready', '/favicon.ico'],
  slowRequestThreshold: 2000,
});
