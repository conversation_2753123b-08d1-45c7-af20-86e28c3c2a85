import { Address } from './address.value-object';

describe('Address Value Object', () => {
  const validAddress = {
    street: 'Rua Teste',
    number: '123',
    complement: 'Apto 45',
    neighborhood: 'Centro',
    city: 'Cidade Teste',
    zipCode: '12345-678',
    state: 'TS',
  };

  describe('constructor', () => {
    it('should create a valid address', () => {
      const address = new Address(
        validAddress.street,
        validAddress.number,
        validAddress.complement,
        validAddress.neighborhood,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      expect(address.street).toBe(validAddress.street);
      expect(address.number).toBe(validAddress.number);
      expect(address.complement).toBe(validAddress.complement);
      expect(address.neighborhood).toBe(validAddress.neighborhood);
      expect(address.city).toBe(validAddress.city);
      expect(address.zipCode).toBe(validAddress.zipCode);
      expect(address.state).toBe(validAddress.state);
    });

    it('should create a valid address with null optional fields', () => {
      const address = new Address(
        validAddress.street,
        null,
        null,
        null,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      expect(address.street).toBe(validAddress.street);
      expect(address.number).toBeNull();
      expect(address.complement).toBeNull();
      expect(address.neighborhood).toBeNull();
      expect(address.city).toBe(validAddress.city);
      expect(address.zipCode).toBe(validAddress.zipCode);
      expect(address.state).toBe(validAddress.state);
    });

    it('should throw error when street is empty', () => {
      expect(() => {
        new Address(
          '',
          validAddress.number,
          validAddress.complement,
          validAddress.neighborhood,
          validAddress.city,
          validAddress.zipCode,
          validAddress.state,
        );
      }).toThrow('Street is required');
    });

    it('should throw error when city is empty', () => {
      expect(() => {
        new Address(
          validAddress.street,
          validAddress.number,
          validAddress.complement,
          validAddress.neighborhood,
          '',
          validAddress.zipCode,
          validAddress.state,
        );
      }).toThrow('City is required');
    });

    it('should throw error when zip code is empty', () => {
      expect(() => {
        new Address(
          validAddress.street,
          validAddress.number,
          validAddress.complement,
          validAddress.neighborhood,
          validAddress.city,
          '',
          validAddress.state,
        );
      }).toThrow('Zip code is required');
    });

    it('should throw error when state is empty', () => {
      expect(() => {
        new Address(
          validAddress.street,
          validAddress.number,
          validAddress.complement,
          validAddress.neighborhood,
          validAddress.city,
          validAddress.zipCode,
          '',
        );
      }).toThrow('State is required');
    });

    it('should throw error when zip code format is invalid', () => {
      const invalidZipCodes = [
        '1234-5678', // 4 digits + hyphen + 4 digits (wrong format)
        '12345-6789', // 5 digits + hyphen + 4 digits (wrong format)
        '12345-67', // 5 digits + hyphen + 2 digits (wrong format)
        '12345-67890', // 5 digits + hyphen + 5 digits (wrong format)
        '123456789', // 9 digits without hyphen (wrong format)
        '1234567', // 7 digits without hyphen (wrong format)
        'abc12345', // Contains letters
        '12345-abc', // Contains letters
      ];

      invalidZipCodes.forEach((invalidZipCode) => {
        expect(() => {
          new Address(
            validAddress.street,
            validAddress.number,
            validAddress.complement,
            validAddress.neighborhood,
            validAddress.city,
            invalidZipCode,
            validAddress.state,
          );
        }).toThrow('Invalid zip code. It must be in format XXXXX-XXX or XXXXXXXX.');
      });
    });

    it('should accept valid zip code formats', () => {
      const validZipCodes = [
        '12345-678', // 5 digits + hyphen + 3 digits
        '12345678', // 8 digits without hyphen
      ];

      validZipCodes.forEach((validZipCode) => {
        expect(() => {
          new Address(
            validAddress.street,
            validAddress.number,
            validAddress.complement,
            validAddress.neighborhood,
            validAddress.city,
            validZipCode,
            validAddress.state,
          );
        }).not.toThrow();
      });
    });
  });

  describe('toJSON', () => {
    it('should return address as JSON object', () => {
      const address = new Address(
        validAddress.street,
        validAddress.number,
        validAddress.complement,
        validAddress.neighborhood,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      const json = address.toJSON();

      expect(json).toEqual({
        street: validAddress.street,
        number: validAddress.number,
        complement: validAddress.complement,
        neighborhood: validAddress.neighborhood,
        city: validAddress.city,
        zipCode: validAddress.zipCode,
        state: validAddress.state,
      });
    });

    it('should return address as JSON object with empty strings for null optional fields', () => {
      const address = new Address(
        validAddress.street,
        null,
        null,
        null,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      const json = address.toJSON();

      expect(json).toEqual({
        street: validAddress.street,
        number: '',
        complement: '',
        neighborhood: '',
        city: validAddress.city,
        zipCode: validAddress.zipCode,
        state: validAddress.state,
      });
    });
  });

  describe('getters', () => {
    it('should return correct values through getters', () => {
      const address = new Address(
        validAddress.street,
        validAddress.number,
        validAddress.complement,
        validAddress.neighborhood,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      expect(address.street).toBe(validAddress.street);
      expect(address.number).toBe(validAddress.number);
      expect(address.complement).toBe(validAddress.complement);
      expect(address.neighborhood).toBe(validAddress.neighborhood);
      expect(address.city).toBe(validAddress.city);
      expect(address.zipCode).toBe(validAddress.zipCode);
      expect(address.state).toBe(validAddress.state);
    });
  });
});
