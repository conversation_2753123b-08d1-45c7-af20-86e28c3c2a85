import { Test, TestingModule } from '@nestjs/testing';
import { CustomerContractController } from './customer-contract.controller';
import { CustomerContractService } from '../services/customer-contract.service';
import { Contract } from '@/modules/documents/domain/entities/contract.entity';
import { EntityType } from '@/modules/documents/domain/enums/entity-type.enum';
import { CreateContractsDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { ContractSignPatchDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { ContractStatus, ContractType } from '@prisma/client';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';

describe('CustomerContractController', () => {
  let controller: CustomerContractController;
  let service: jest.Mocked<CustomerContractService>;

  const mockContract: Contract = {
    uuid: 'contract-uuid',
    entityUuid: 'customer-uuid',
    entityType: EntityType.CLIENT,
    status: ContractStatus.PENDING,
    contractType: ContractType.CERT_PLATFORM,
    createdBy: 'user-id',
    uptadedBy: 'user-id',
    currentVersion: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    versions: [
      {
        id: 'version-id',
        versionId: 1,
        filePath: '/path/to/file.pdf',
        uploadedAt: new Date(),
        uploadedBy: 'user-id',
        signed: false,
        validatedBy: null,
        validatedAt: null,
        startDate: null,
        expirationDate: null,
        observations: null,
        contractId: 'contract-uuid',
        createdAt: new Date(),
      },
    ],
  };

  const mockFiles = [
    {
      fieldname: 'files',
      originalname: 'test.pdf',
      encoding: '7bit',
      mimetype: 'application/pdf',
      buffer: Buffer.from('test content'),
      size: 1024,
    } as Express.Multer.File,
  ];

  const mockFormData = {
    contractsMetadata: JSON.stringify([
      {
        contractIdentifier: 'test-contract',
        entityType: EntityType.CLIENT,
        entityActualUuid: 'customer-uuid',
        contractType: 'CERT_PLATFORM',
        signed: false,
        expirationDate: '2025-12-31',
      },
    ]),
  };

  const mockRequest = {
    user: {
      id: 'user-id',
    },
  };

  beforeEach(async () => {
    const mockCustomerContractService = {
      createCustomerContract: jest.fn(),
      listCustomerContracts: jest.fn(),
      getCustomerContract: jest.fn(),
      updateCustomerContract: jest.fn(),
      deleteCustomerContract: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomerContractController],
      providers: [
        {
          provide: CustomerContractService,
          useValue: mockCustomerContractService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(RolesGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<CustomerContractController>(CustomerContractController);
    service = module.get(CustomerContractService);
  });

  describe('createContracts', () => {
    it('should create contracts successfully', async () => {
      service.createCustomerContract.mockResolvedValue([mockContract]);

      const result = await controller.createContracts(
        'customer-uuid',
        mockFiles,
        mockFormData,
        mockRequest as any,
      );

      expect(service.createCustomerContract).toHaveBeenCalledWith(
        'customer-uuid',
        mockFiles,
        'user-id',
        expect.objectContaining({
          contracts: expect.arrayContaining([
            expect.objectContaining({
              entityUuid: 'customer-uuid',
              uploadedBy: 'user-id',
            }),
          ]),
        }),
      );
      expect(result).toHaveLength(1);
    });

    it('should throw error when no files are provided', async () => {
      await expect(
        controller.createContracts('customer-uuid', [], mockFormData, mockRequest as any),
      ).rejects.toThrow('Pelo menos um arquivo deve ser enviado.');
    });

    it('should throw error when contractsMetadata is invalid JSON', async () => {
      const invalidFormData = {
        contractsMetadata: 'invalid-json',
      };

      await expect(
        controller.createContracts('customer-uuid', mockFiles, invalidFormData, mockRequest as any),
      ).rejects.toThrow('O campo contractsMetadata não é uma string JSON válida.');
    });

    it('should throw error when contractsMetadata is not an array', async () => {
      const invalidFormData = {
        contractsMetadata: JSON.stringify('not-an-array'),
      };

      await expect(
        controller.createContracts('customer-uuid', mockFiles, invalidFormData, mockRequest as any),
      ).rejects.toThrow('O número de metadatos de contrato não corresponde ao número de arquivos enviados.');
    });

    it('should throw error when number of files does not match metadata', async () => {
      const invalidFormData = {
        contractsMetadata: JSON.stringify([
          { contractIdentifier: 'contract1' },
          { contractIdentifier: 'contract2' },
        ]),
      };

      await expect(
        controller.createContracts('customer-uuid', mockFiles, invalidFormData, mockRequest as any),
      ).rejects.toThrow('O número de metadatos de contrato não corresponde ao número de arquivos enviados.');
    });
  });

  describe('getContracts', () => {
    it('should list customer contracts successfully', async () => {
      service.listCustomerContracts.mockResolvedValue([mockContract]);

      const result = await controller.getContracts('customer-uuid');

      expect(service.listCustomerContracts).toHaveBeenCalledWith('customer-uuid');
      expect(result).toHaveLength(1);
    });
  });

  describe('getContract', () => {
    it('should get specific customer contract successfully', async () => {
      service.getCustomerContract.mockResolvedValue(mockContract);

      const result = await controller.getContract('customer-uuid', 'contract-uuid');

      expect(service.getCustomerContract).toHaveBeenCalledWith('customer-uuid', 'contract-uuid');
      expect(result).toBeDefined();
    });

    it('should throw error when contract is not found', async () => {
      service.getCustomerContract.mockResolvedValue(null);

      await expect(
        controller.getContract('customer-uuid', 'non-existent-uuid'),
      ).rejects.toThrow('Contract not found');
    });
  });

  describe('patchContract', () => {
    const mockPatchDto: ContractSignPatchDto = {
      isSigned: true,
    };

    it('should update customer contract successfully', async () => {
      service.updateCustomerContract.mockResolvedValue(mockContract);

      const result = await controller.patchContract(
        'customer-uuid',
        'contract-uuid',
        mockPatchDto,
      );

      expect(service.updateCustomerContract).toHaveBeenCalledWith(
        'customer-uuid',
        'contract-uuid',
        mockPatchDto,
      );
      expect(result).toBeDefined();
    });
  });

  describe('deleteContract', () => {
    it('should delete customer contract successfully', async () => {
      service.deleteCustomerContract.mockResolvedValue(undefined);

      await controller.deleteContract('customer-uuid', 'contract-uuid');

      expect(service.deleteCustomerContract).toHaveBeenCalledWith('customer-uuid', 'contract-uuid');
    });
  });
}); 