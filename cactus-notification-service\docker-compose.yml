version: '3.8'

services:
  # PostgreSQL Database para notificações
  postgres:
    image: postgres:15-alpine
    container_name: cactus-notifications-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: cactus_notifications
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - cactus-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres -d cactus_notifications']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis para BullMQ
  redis:
    image: redis:7
    container_name: cactus-notifications-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    networks:
      - cactus-network

  # Cactus Notification Service API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cactus-notifications-api
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: 3000
      DATABASE_URL: '********************************************/cactus_notifications?schema=notifications'
      BACKOFFICE_DATABASE_URL: ${BACKOFFICE_DATABASE_URL}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      BCRYPT_ROUNDS: ${BCRYPT_ROUNDS:-10}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CONTRACT_EXPIRATION_DAYS_AHEAD: '30,15,7,1'
      CONTRACT_CHECK_CRON: '0 * * * *'
      OTEL_EXPORTER_OTLP_ENDPOINT: http://jaeger:4318/v1/traces
    ports:
      - '3000:3000'
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - cactus-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
      - ./logs:/app/logs
    command: sh -c "npm run db:migrate && npm run db:seed && npm run dev"
    stdin_open: true
    tty: true

  jaeger:
    image: jaegertracing/all-in-one:1.56
    container_name: jaeger
    ports:
      - '16686:16686' # UI Web
      - '4318:4318' # OTLP HTTP (recebe traces do OpenTelemetry)
    networks:
      - fast-white-network

  # Prometheus para coleta de métricas
  prometheus:
    image: prom/prometheus:latest
    container_name: fast-white-prometheus
    ports:
      - '9090:9090'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    depends_on:
      api:
        condition: service_started
    restart: unless-stopped
    networks:
      - fast-white-network

  # Grafana para visualização de métricas
  grafana:
    image: grafana/grafana:latest
    container_name: fast-white-grafana
    ports:
      - '3001:3000'
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      prometheus:
        condition: service_started
    restart: unless-stopped
    networks:
      - fast-white-network
  # Worker BullMQ para NASA
  worker-nasa:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: worker-nasa
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-fastwhite}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres123}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      BCRYPT_ROUNDS: ${BCRYPT_ROUNDS:-10}
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - fast-white-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
      - ./logs:/app/logs
    command: pnpm run worker:nasa
    stdin_open: true
    tty: true

volumes:
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  cactus-network:
    driver: bridge
