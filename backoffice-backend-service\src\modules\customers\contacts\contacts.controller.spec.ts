import { Test, TestingModule } from '@nestjs/testing';
import { ContactsController } from './contacts.controller';
import { ContactsService } from './contacts.service';
import { CreateCustomerContactUseCase } from '@/core/application/use-cases/customer/create-customer-contact.use-case';
import { FindCustomerByUuidUseCase } from '../application/use-cases/find-customer-by-uuid.use-case';
import { ListCustomerContactsUseCase } from '@/core/application/use-cases/customer/list-customer-contacts.use-case';
import { DeleteCustomerContactUseCase } from '@/core/application/use-cases/customer/delete-customer-contact.use-case';
import { UpdateCustomerContactUseCase } from '@/core/application/use-cases/customer/update-customer-contact.use-case';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { ConfigService } from '@nestjs/config';

describe('ContactsController', () => {
  let controller: ContactsController;
  let service: jest.Mocked<ContactsService>;

  beforeEach(async () => {
    const mockService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContactsController],
      providers: [
        {
          provide: ContactsService,
          useValue: mockService,
        },
        {
          provide: FindCustomerByUuidUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: CreateCustomerContactUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListCustomerContactsUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: DeleteCustomerContactUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: UpdateCustomerContactUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: JwtAuthGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
        {
          provide: RolesGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
      ],
    }).compile();

    controller = module.get<ContactsController>(ContactsController);
    service = module.get(ContactsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create contacts', async () => {
      const uuid = 'test-uuid';
      const createContactDto = [
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Finance',
          responsible: 'Maria Silva',
        },
      ];

      const mockResponse = [
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Finance',
          responsible: 'Maria Silva',
        },
      ];

      service.create.mockResolvedValue(mockResponse);

      const result = await controller.create(uuid, createContactDto);

      expect(result).toEqual(mockResponse);
      expect(service.create).toHaveBeenCalledWith(uuid, createContactDto);
    });
  });

  describe('findAll', () => {
    it('should return all contacts for a customer', async () => {
      const uuid = 'test-uuid';
      const mockResponse = {
        contacts: [
          {
            id: '1',
            contact: '<EMAIL>',
            type: 'email',
            area: 'Finance',
            responsible: 'Maria Silva',
          },
        ],
      };

      service.findAll.mockResolvedValue(mockResponse);

      const result = await controller.findAll(uuid);

      expect(result).toEqual(mockResponse);
      expect(service.findAll).toHaveBeenCalledWith(uuid);
    });
  });

  describe('update', () => {
    it('should update a contact', async () => {
      const id = '1';
      const updateContactDto = {
        contact: '<EMAIL>',
        type: 'email',
        area: 'HR',
        responsible: 'João Silva',
      };

      const mockResponse = {
        id: '1',
        contact: '<EMAIL>',
        type: 'email',
        area: 'HR',
        responsible: 'João Silva',
        customerId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      service.update.mockResolvedValue(mockResponse);

      const result = await controller.update(id, updateContactDto);

      expect(result).toEqual(mockResponse);
      expect(service.update).toHaveBeenCalledWith(id, updateContactDto);
    });
  });

  describe('remove', () => {
    it('should remove a contact', async () => {
      const id = '1';

      service.remove.mockResolvedValue(undefined);

      const result = await controller.remove(id);

      expect(result).toBeUndefined();
      expect(service.remove).toHaveBeenCalledWith(id);
    });
  });
});
