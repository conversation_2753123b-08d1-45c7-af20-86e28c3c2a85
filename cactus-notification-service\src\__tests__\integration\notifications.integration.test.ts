import Fastify, { FastifyInstance } from 'fastify';
import { NotificationListenerService } from '../../modules/notifications/services/notification-listener.service';
import { notificationRoutes } from '../../modules/notifications/routes/notification.routes';

// Mock das dependências externas
jest.mock('../../shared/database/prisma', () => ({
  prismaLocal: {
    notification: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      count: jest.fn(),
      findFirst: jest.fn(),
    },
    userNotificationSettings: {
      findUnique: jest.fn(),
      create: jest.fn(),
      upsert: jest.fn(),
    },
  },
  notifyNewNotification: jest.fn(),
  connectDatabases: jest.fn(),
  disconnectDatabases: jest.fn(),
}));

jest.mock('../../modules/notifications/services/notification-listener.service');

describe('Notifications Integration Tests', () => {
  let app: FastifyInstance;
  let mockListenerService: jest.Mocked<NotificationListenerService>;

  beforeAll(async () => {
    mockListenerService = new NotificationListenerService() as jest.Mocked<NotificationListenerService>;
    mockListenerService.getConnectionStats.mockReturnValue({
      totalConnections: 0,
      userConnections: {},
    });

    app = Fastify();
    
    await app.register(async (fastify) => {
      await notificationRoutes(fastify, mockListenerService);
    }, { prefix: '/api' });

    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /api/health', () => {
    it('should return health status', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/health',
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        status: 'healthy',
        timestamp: expect.any(String),
        connections: 0,
        service: 'cactus-notification-service',
      });
    });
  });

  describe('GET /api/notifications', () => {
    it('should require user ID header', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/notifications',
      });

      expect(response.statusCode).toBe(500); // Será 500 porque não há user-id
    });

    it('should return notifications with user ID', async () => {
      const { prismaLocal } = require('../../shared/database/prisma');
      
      prismaLocal.notification.findMany.mockResolvedValue([]);
      prismaLocal.notification.count.mockResolvedValue(0);

      const response = await app.inject({
        method: 'GET',
        url: '/api/notifications',
        headers: {
          'x-user-id': 'test-user',
        },
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        notifications: [],
        unreadCount: 0,
        pagination: {
          limit: 20,
          offset: 0,
          total: 0,
        },
      });
    });
  });

  describe('GET /api/notifications/unread-count', () => {
    it('should return unread count', async () => {
      const { prismaLocal } = require('../../shared/database/prisma');
      
      prismaLocal.notification.count.mockResolvedValue(5);

      const response = await app.inject({
        method: 'GET',
        url: '/api/notifications/unread-count',
        headers: {
          'x-user-id': 'test-user',
        },
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toEqual({
        unreadCount: 5,
      });
    });
  });

  describe('PATCH /api/notifications/:notificationId/read', () => {
    it('should mark notification as read', async () => {
      const { prismaLocal } = require('../../shared/database/prisma');
      
      const mockNotification = {
        id: 'notification-1',
        userId: 'test-user',
        type: 'CONTRACT_EXPIRING',
        title: 'Test',
        message: 'Message',
        data: {},
        isRead: true,
        readAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaLocal.notification.update.mockResolvedValue(mockNotification);

      const response = await app.inject({
        method: 'PATCH',
        url: '/api/notifications/notification-1/read',
        headers: {
          'x-user-id': 'test-user',
        },
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        success: true,
        notification: expect.objectContaining({
          id: 'notification-1',
          isRead: true,
        }),
      });
    });
  });

  describe('PATCH /api/notifications/read-multiple', () => {
    it('should mark multiple notifications as read', async () => {
      const { prismaLocal } = require('../../shared/database/prisma');
      
      prismaLocal.notification.updateMany.mockResolvedValue({ count: 2 });

      const response = await app.inject({
        method: 'PATCH',
        url: '/api/notifications/read-multiple',
        headers: {
          'x-user-id': 'test-user',
          'content-type': 'application/json',
        },
        payload: {
          notificationIds: ['notification-1', 'notification-2'],
        },
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toEqual({
        success: true,
        markedCount: 2,
      });
    });

    it('should validate request body', async () => {
      const response = await app.inject({
        method: 'PATCH',
        url: '/api/notifications/read-multiple',
        headers: {
          'x-user-id': 'test-user',
          'content-type': 'application/json',
        },
        payload: {
          notificationIds: 'invalid',
        },
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        error: 'notificationIds must be an array',
      });
    });
  });

  describe('GET /api/notifications/settings', () => {
    it('should return user settings', async () => {
      const { prismaLocal } = require('../../shared/database/prisma');
      
      const mockSettings = {
        id: 'settings-1',
        userId: 'test-user',
        contractExpiration: true,
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaLocal.userNotificationSettings.findUnique.mockResolvedValue(mockSettings);

      const response = await app.inject({
        method: 'GET',
        url: '/api/notifications/settings',
        headers: {
          'x-user-id': 'test-user',
        },
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        settings: expect.objectContaining({
          userId: 'test-user',
          contractExpiration: true,
        }),
      });
    });
  });

  describe('PUT /api/notifications/settings', () => {
    it('should update user settings', async () => {
      const { prismaLocal } = require('../../shared/database/prisma');
      
      const mockSettings = {
        id: 'settings-1',
        userId: 'test-user',
        contractExpiration: false,
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaLocal.userNotificationSettings.upsert.mockResolvedValue(mockSettings);

      const response = await app.inject({
        method: 'PUT',
        url: '/api/notifications/settings',
        headers: {
          'x-user-id': 'test-user',
          'content-type': 'application/json',
        },
        payload: {
          contractExpiration: false,
          pushNotifications: false,
        },
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        success: true,
        settings: expect.objectContaining({
          contractExpiration: false,
          pushNotifications: false,
        }),
      });
    });
  });

  describe('GET /api/notifications/stats', () => {
    it('should return connection statistics', async () => {
      mockListenerService.getConnectionStats.mockReturnValue({
        totalConnections: 3,
        userConnections: {
          'user-1': 1,
          'user-2': 2,
        },
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/notifications/stats',
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body).toMatchObject({
        stats: {
          totalConnections: 3,
          userConnections: {
            'user-1': 1,
            'user-2': 2,
          },
        },
      });
    });
  });
});
