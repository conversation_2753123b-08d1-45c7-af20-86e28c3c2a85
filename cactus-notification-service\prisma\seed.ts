import { prismaLocal } from '../src/shared/database/prisma';
import { logger } from '../src/shared/logger/logger';

async function main() {
  logger.info('Starting database seed...');

  // Criar configurações padrão para usuários de exemplo
  const defaultSettings = [
    {
      userId: 'admin-user-id',
      contractExpiration: true,
      documentExpiration: true,
      emailNotifications: true,
      pushNotifications: true,
    },
    {
      userId: 'test-user-id',
      contractExpiration: true,
      documentExpiration: false,
      emailNotifications: true,
      pushNotifications: false,
    },
  ];

  for (const setting of defaultSettings) {
    await prismaLocal.userNotificationSettings.upsert({
      where: { userId: setting.userId },
      update: setting,
      create: setting,
    });
  }

  // Criar algumas notificações de exemplo
  const sampleNotifications = [
    {
      userId: 'admin-user-id',
      type: 'CONTRACT_EXPIRING' as const,
      title: 'Contrato vencendo em 30 dias',
      message: 'O contrato CERT_PLATFORM da empresa XYZ vence em 30 dias.',
      data: {
        contractId: 'contract-123',
        entityName: 'Empresa XYZ',
        contractType: 'CERT_PLATFORM',
        expirationDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      },
    },
    {
      userId: 'test-user-id',
      type: 'CONTRACT_EXPIRING' as const,
      title: 'Contrato vencendo em 7 dias',
      message: 'O contrato CERT_GAME da empresa ABC vence em 7 dias.',
      data: {
        contractId: 'contract-456',
        entityName: 'Empresa ABC',
        contractType: 'CERT_GAME',
        expirationDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      },
    },
  ];

  for (const notification of sampleNotifications) {
    await prismaLocal.notification.create({
      data: notification,
    });
  }

  logger.info('Database seed completed successfully!');
}

main()
  .catch((e) => {
    logger.error('Error during seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prismaLocal.$disconnect();
  });
