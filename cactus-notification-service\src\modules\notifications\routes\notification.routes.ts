import { FastifyInstance } from 'fastify';
import { NotificationController } from '../controllers/notification.controller';
import { NotificationListenerService } from '../services/notification-listener.service';

// Schemas para validação e documentação
const notificationSchema = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    userId: { type: 'string' },
    type: { 
      type: 'string',
      enum: ['CONTRACT_EXPIRING', 'CONTRACT_EXPIRED', 'DOCUMENT_EXPIRING', 'DOCUMENT_EXPIRED', 'SYSTEM_ALERT']
    },
    title: { type: 'string' },
    message: { type: 'string' },
    data: { type: 'object' },
    isRead: { type: 'boolean' },
    readAt: { type: 'string', format: 'date-time', nullable: true },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
  },
};

const userSettingsSchema = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    userId: { type: 'string' },
    contractExpiration: { type: 'boolean' },
    documentExpiration: { type: 'boolean' },
    emailNotifications: { type: 'boolean' },
    pushNotifications: { type: 'boolean' },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
  },
};

export async function notificationRoutes(
  fastify: FastifyInstance,
  listenerService: NotificationListenerService
) {
  const controller = new NotificationController(listenerService);

  // Registrar schemas
  fastify.addSchema({
    $id: 'notification',
    ...notificationSchema,
  });

  fastify.addSchema({
    $id: 'userSettings',
    ...userSettingsSchema,
  });

  // SSE endpoint para notificações em tempo real
  fastify.get('/notifications/stream', {
    schema: {
      description: 'Server-Sent Events endpoint for real-time notifications',
      tags: ['notifications'],
      headers: {
        type: 'object',
        properties: {
          'x-user-id': { type: 'string', description: 'User ID for authentication' },
        },
        required: ['x-user-id'],
      },
      response: {
        200: {
          description: 'SSE stream of notifications',
          type: 'string',
        },
      },
    },
  }, controller.streamNotifications.bind(controller));

  // Buscar notificações
  fastify.get('/notifications', {
    schema: {
      description: 'Get user notifications with filters',
      tags: ['notifications'],
      headers: {
        type: 'object',
        properties: {
          'x-user-id': { type: 'string' },
        },
        required: ['x-user-id'],
      },
      querystring: {
        type: 'object',
        properties: {
          type: { 
            type: 'string',
            enum: ['CONTRACT_EXPIRING', 'CONTRACT_EXPIRED', 'DOCUMENT_EXPIRING', 'DOCUMENT_EXPIRED', 'SYSTEM_ALERT']
          },
          isRead: { type: 'boolean' },
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          offset: { type: 'integer', minimum: 0, default: 0 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            notifications: {
              type: 'array',
              items: { $ref: 'notification#' },
            },
            unreadCount: { type: 'integer' },
            pagination: {
              type: 'object',
              properties: {
                limit: { type: 'integer' },
                offset: { type: 'integer' },
                total: { type: 'integer' },
              },
            },
          },
        },
      },
    },
  }, controller.getNotifications.bind(controller));

  // Marcar notificação como lida
  fastify.patch('/notifications/:notificationId/read', {
    schema: {
      description: 'Mark a notification as read',
      tags: ['notifications'],
      headers: {
        type: 'object',
        properties: {
          'x-user-id': { type: 'string' },
        },
        required: ['x-user-id'],
      },
      params: {
        type: 'object',
        properties: {
          notificationId: { type: 'string' },
        },
        required: ['notificationId'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            notification: { $ref: 'notification#' },
          },
        },
      },
    },
  }, controller.markAsRead.bind(controller));

  // Marcar múltiplas notificações como lidas
  fastify.patch('/notifications/read-multiple', {
    schema: {
      description: 'Mark multiple notifications as read',
      tags: ['notifications'],
      headers: {
        type: 'object',
        properties: {
          'x-user-id': { type: 'string' },
        },
        required: ['x-user-id'],
      },
      body: {
        type: 'object',
        properties: {
          notificationIds: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
          },
        },
        required: ['notificationIds'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            markedCount: { type: 'integer' },
          },
        },
      },
    },
  }, controller.markMultipleAsRead.bind(controller));

  // Obter contagem de não lidas
  fastify.get('/notifications/unread-count', {
    schema: {
      description: 'Get unread notifications count',
      tags: ['notifications'],
      headers: {
        type: 'object',
        properties: {
          'x-user-id': { type: 'string' },
        },
        required: ['x-user-id'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            unreadCount: { type: 'integer' },
          },
        },
      },
    },
  }, controller.getUnreadCount.bind(controller));

  // Configurações do usuário
  fastify.get('/notifications/settings', {
    schema: {
      description: 'Get user notification settings',
      tags: ['notifications'],
      headers: {
        type: 'object',
        properties: {
          'x-user-id': { type: 'string' },
        },
        required: ['x-user-id'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            settings: { $ref: 'userSettings#' },
          },
        },
      },
    },
  }, controller.getUserSettings.bind(controller));

  fastify.put('/notifications/settings', {
    schema: {
      description: 'Update user notification settings',
      tags: ['notifications'],
      headers: {
        type: 'object',
        properties: {
          'x-user-id': { type: 'string' },
        },
        required: ['x-user-id'],
      },
      body: {
        type: 'object',
        properties: {
          contractExpiration: { type: 'boolean' },
          documentExpiration: { type: 'boolean' },
          emailNotifications: { type: 'boolean' },
          pushNotifications: { type: 'boolean' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            settings: { $ref: 'userSettings#' },
          },
        },
      },
    },
  }, controller.updateUserSettings.bind(controller));

  // Estatísticas das conexões (admin)
  fastify.get('/notifications/stats', {
    schema: {
      description: 'Get SSE connection statistics (admin only)',
      tags: ['admin'],
      response: {
        200: {
          type: 'object',
          properties: {
            stats: {
              type: 'object',
              properties: {
                totalConnections: { type: 'integer' },
                userConnections: { type: 'object' },
              },
            },
          },
        },
      },
    },
  }, controller.getConnectionStats.bind(controller));

  // Health check
  fastify.get('/health', {
    schema: {
      description: 'Health check endpoint',
      tags: ['health'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            connections: { type: 'integer' },
            service: { type: 'string' },
          },
        },
      },
    },
  }, controller.healthCheck.bind(controller));
}
