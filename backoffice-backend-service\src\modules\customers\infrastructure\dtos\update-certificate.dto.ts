import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, Matches } from 'class-validator';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';

export class UpdateCertificateDto {
  @ApiPropertyOptional({
    description: 'Nova categoria do certificado',
    enum: CertificateCategory,
  })
  @IsEnum(CertificateCategory)
  @IsOptional()
  category?: CertificateCategory;

  @ApiPropertyOptional({
    description: 'Novo tipo do certificado',
    enum: CertificateType,
  })
  @IsEnum(CertificateType)
  @IsOptional()
  type?: CertificateType;

  @ApiPropertyOptional({
    description: 'Novas observações sobre o certificado',
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Data de emissão do certificado',
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'A data deve estar no formato YYYY-MM-DD',
  })
  emissionDate?: string;

  @ApiPropertyOptional({
    description: 'Data de validade do certificado',
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'A data deve estar no formato YYYY-MM-DD',
  })
  expirationDate?: string;
}
