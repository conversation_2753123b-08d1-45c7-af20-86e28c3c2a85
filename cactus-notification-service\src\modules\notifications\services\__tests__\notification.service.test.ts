import { NotificationService } from '../notification.service';
import { prismaLocal } from '../../../../shared/database/prisma';
import { NotificationType } from '@prisma/client';

// Mock do Prisma
jest.mock('../../../../shared/database/prisma', () => ({
  prismaLocal: {
    notification: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      count: jest.fn(),
      findFirst: jest.fn(),
    },
    userNotificationSettings: {
      findUnique: jest.fn(),
      create: jest.fn(),
      upsert: jest.fn(),
    },
  },
  notifyNewNotification: jest.fn(),
}));

describe('NotificationService', () => {
  let notificationService: NotificationService;
  const mockPrisma = prismaLocal as jest.Mocked<typeof prismaLocal>;

  beforeEach(() => {
    notificationService = new NotificationService();
    jest.clearAllMocks();
  });

  describe('createNotification', () => {
    it('should create a notification successfully', async () => {
      const mockNotification = {
        id: 'notification-1',
        userId: 'user-1',
        type: 'CONTRACT_EXPIRING' as NotificationType,
        title: 'Test Notification',
        message: 'Test message',
        data: { contractId: 'contract-1' },
        isRead: false,
        readAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockSettings = {
        id: 'settings-1',
        userId: 'user-1',
        contractExpiration: true,
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.userNotificationSettings.findUnique.mockResolvedValue(mockSettings);
      mockPrisma.notification.findFirst.mockResolvedValue(null); // No similar notification
      mockPrisma.notification.create.mockResolvedValue(mockNotification);

      const result = await notificationService.createNotification({
        userId: 'user-1',
        type: 'CONTRACT_EXPIRING',
        title: 'Test Notification',
        message: 'Test message',
        data: { contractId: 'contract-1' },
      });

      expect(result).toEqual(mockNotification);
      expect(mockPrisma.notification.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          type: 'CONTRACT_EXPIRING',
          title: 'Test Notification',
          message: 'Test message',
          data: { contractId: 'contract-1' },
        },
      });
    });

    it('should skip notification if user has disabled contract notifications', async () => {
      const mockSettings = {
        id: 'settings-1',
        userId: 'user-1',
        contractExpiration: false, // Disabled
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.userNotificationSettings.findUnique.mockResolvedValue(mockSettings);

      await expect(
        notificationService.createNotification({
          userId: 'user-1',
          type: 'CONTRACT_EXPIRING',
          title: 'Test Notification',
          message: 'Test message',
        })
      ).rejects.toThrow('User has disabled this type of notification');

      expect(mockPrisma.notification.create).not.toHaveBeenCalled();
    });
  });

  describe('findNotifications', () => {
    it('should find notifications with filters', async () => {
      const mockNotifications = [
        {
          id: 'notification-1',
          userId: 'user-1',
          type: 'CONTRACT_EXPIRING' as NotificationType,
          title: 'Test 1',
          message: 'Message 1',
          data: {},
          isRead: false,
          readAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrisma.notification.findMany.mockResolvedValue(mockNotifications);

      const result = await notificationService.findNotifications({
        userId: 'user-1',
        isRead: false,
        limit: 10,
      });

      expect(result).toEqual(mockNotifications);
      expect(mockPrisma.notification.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-1',
          isRead: false,
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        skip: 0,
      });
    });
  });

  describe('markAsRead', () => {
    it('should mark notification as read', async () => {
      const mockNotification = {
        id: 'notification-1',
        userId: 'user-1',
        type: 'CONTRACT_EXPIRING' as NotificationType,
        title: 'Test',
        message: 'Message',
        data: {},
        isRead: true,
        readAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.notification.update.mockResolvedValue(mockNotification);

      const result = await notificationService.markAsRead('notification-1', 'user-1');

      expect(result).toEqual(mockNotification);
      expect(mockPrisma.notification.update).toHaveBeenCalledWith({
        where: {
          id: 'notification-1',
          userId: 'user-1',
        },
        data: {
          isRead: true,
          readAt: expect.any(Date),
        },
      });
    });
  });

  describe('getUserSettings', () => {
    it('should return existing user settings', async () => {
      const mockSettings = {
        id: 'settings-1',
        userId: 'user-1',
        contractExpiration: true,
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.userNotificationSettings.findUnique.mockResolvedValue(mockSettings);

      const result = await notificationService.getUserSettings('user-1');

      expect(result).toEqual(mockSettings);
    });

    it('should create default settings if none exist', async () => {
      const mockSettings = {
        id: 'settings-1',
        userId: 'user-1',
        contractExpiration: true,
        documentExpiration: true,
        emailNotifications: true,
        pushNotifications: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.userNotificationSettings.findUnique.mockResolvedValue(null);
      mockPrisma.userNotificationSettings.create.mockResolvedValue(mockSettings);

      const result = await notificationService.getUserSettings('user-1');

      expect(result).toEqual(mockSettings);
      expect(mockPrisma.userNotificationSettings.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          contractExpiration: true,
          documentExpiration: true,
          emailNotifications: true,
          pushNotifications: true,
        },
      });
    });
  });

  describe('getUnreadCount', () => {
    it('should return unread count', async () => {
      mockPrisma.notification.count.mockResolvedValue(5);

      const result = await notificationService.getUnreadCount('user-1');

      expect(result).toBe(5);
      expect(mockPrisma.notification.count).toHaveBeenCalledWith({
        where: {
          userId: 'user-1',
          isRead: false,
        },
      });
    });
  });
});
