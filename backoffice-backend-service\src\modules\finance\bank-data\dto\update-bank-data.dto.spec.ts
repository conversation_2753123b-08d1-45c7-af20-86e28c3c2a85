import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { UpdateBankDataDto } from './update-bank-data.dto';
import {
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

describe('UpdateBankDataDto', () => {
  describe('Valid inputs', () => {
    it('should validate with all fields', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: '<PERSON>',
        accountHolderDocument: '**********1',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: false,
        status: BankDataStatus.ACTIVE,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with partial fields', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        accountType: BankAccountType.SAVINGS,
        accountHolderName: 'João da Silva',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with empty object (all fields optional)', async () => {
      const dto = plainToClass(UpdateBankDataDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with bank code in format 123-4', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankCode: '001-2',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with different account types', async () => {
      const accountTypes = [BankAccountType.CHECKING, BankAccountType.SAVINGS];

      for (const accountType of accountTypes) {
        const dto = plainToClass(UpdateBankDataDto, {
          accountType,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate with different PIX key types', async () => {
      const pixKeyTypes = [
        BankPixKeyType.EMAIL,
        BankPixKeyType.PHONE,
        BankPixKeyType.CPF,
        BankPixKeyType.CNPJ,
        BankPixKeyType.RANDOM,
      ];

      for (const pixKeyType of pixKeyTypes) {
        const dto = plainToClass(UpdateBankDataDto, {
          pixKey: '<EMAIL>',
          pixKeyType,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate with CNPJ document (14 digits)', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        accountHolderDocument: '**************',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with different status values', async () => {
      const statuses = [BankDataStatus.ACTIVE, BankDataStatus.INACTIVE];

      for (const status of statuses) {
        const dto = plainToClass(UpdateBankDataDto, {
          status,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });
  });

  describe('Invalid inputs', () => {
    it('should fail validation when bankName is empty string', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankName: '',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankNameError = errors.find(
        (error) => error.property === 'bankName',
      );
      expect(bankNameError).toBeDefined();
    });

    it('should fail validation when bankCode is invalid format', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankCode: 'ABC',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankCodeError = errors.find(
        (error) => error.property === 'bankCode',
      );
      expect(bankCodeError).toBeDefined();
      expect(Object.values(bankCodeError?.constraints || {})).toContain(
        'Bank code must contain only numbers or be in the format 123-4',
      );
    });

    it('should fail validation when agencyNumber contains non-numeric characters', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        agencyNumber: '12A4',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const agencyNumberError = errors.find(
        (error) => error.property === 'agencyNumber',
      );
      expect(agencyNumberError).toBeDefined();
      expect(Object.values(agencyNumberError?.constraints || {})).toContain(
        'Agency number must contain only numbers',
      );
    });

    it('should fail validation when accountNumber contains non-numeric characters', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        accountNumber: '123A56',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const accountNumberError = errors.find(
        (error) => error.property === 'accountNumber',
      );
      expect(accountNumberError).toBeDefined();
      expect(Object.values(accountNumberError?.constraints || {})).toContain(
        'Account number must contain only numbers',
      );
    });

    it('should fail validation when accountDigit contains non-numeric characters', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        accountDigit: 'X',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const accountDigitError = errors.find(
        (error) => error.property === 'accountDigit',
      );
      expect(accountDigitError).toBeDefined();
      expect(Object.values(accountDigitError?.constraints || {})).toContain(
        'Account digit must contain only numbers',
      );
    });

    it('should fail validation when accountHolderDocument is too short', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        accountHolderDocument: '*********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const documentError = errors.find(
        (error) => error.property === 'accountHolderDocument',
      );
      expect(documentError).toBeDefined();
      expect(Object.values(documentError?.constraints || {})).toContain(
        'Account holder document must contain only numbers and be between 11 and 14 digits long',
      );
    });

    it('should fail validation when accountHolderDocument is too long', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        accountHolderDocument: '**********12345',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const documentError = errors.find(
        (error) => error.property === 'accountHolderDocument',
      );
      expect(documentError).toBeDefined();
      expect(Object.values(documentError?.constraints || {})).toContain(
        'Account holder document must be at most 14 characters long',
      );
    });

    it('should fail validation when bankName exceeds max length', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankName: 'a'.repeat(101),
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankNameError = errors.find(
        (error) => error.property === 'bankName',
      );
      expect(bankNameError).toBeDefined();
      expect(Object.values(bankNameError?.constraints || {})).toContain(
        'Bank name must be at most 100 characters long',
      );
    });

    it('should fail validation when bankCode exceeds max length', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankCode: '**********1',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankCodeError = errors.find(
        (error) => error.property === 'bankCode',
      );
      expect(bankCodeError).toBeDefined();
      expect(Object.values(bankCodeError?.constraints || {})).toContain(
        'Bank code must be at most 10 characters long',
      );
    });

    it('should fail validation for invalid account type', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        accountType: 'INVALID_TYPE' as BankAccountType,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const accountTypeError = errors.find(
        (error) => error.property === 'accountType',
      );
      expect(accountTypeError).toBeDefined();
      expect(Object.values(accountTypeError?.constraints || {})).toContain(
        'Account type must be a valid value',
      );
    });

    it('should fail validation for invalid PIX key type', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        pixKeyType: 'INVALID_TYPE' as BankPixKeyType,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const pixKeyTypeError = errors.find(
        (error) => error.property === 'pixKeyType',
      );
      expect(pixKeyTypeError).toBeDefined();
      expect(Object.values(pixKeyTypeError?.constraints || {})).toContain(
        'PIX key type must be a valid value',
      );
    });

    it('should fail validation for invalid status', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        status: 'INVALID_STATUS' as BankDataStatus,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const statusError = errors.find((error) => error.property === 'status');
      expect(statusError).toBeDefined();
      expect(Object.values(statusError?.constraints || {})).toContain(
        'Status must be a valid enum value',
      );
    });

    it('should fail validation for invalid isDigitalBank type', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        isDigitalBank: 'not-a-boolean' as unknown as boolean | undefined,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const isDigitalBankError = errors.find(
        (error) => error.property === 'isDigitalBank',
      );
      expect(isDigitalBankError).toBeDefined();
      expect(Object.values(isDigitalBankError?.constraints || {})).toContain(
        'Digital bank indicator must be a boolean value',
      );
    });

    it('should fail validation when pixKey exceeds max length', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        pixKey: 'a'.repeat(101),
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const pixKeyError = errors.find((error) => error.property === 'pixKey');
      expect(pixKeyError).toBeDefined();
      expect(Object.values(pixKeyError?.constraints || {})).toContain(
        'PIX key must be at most 100 characters long',
      );
    });
  });

  describe('Field length validations', () => {
    it('should validate maximum lengths for all string fields', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankName: 'a'.repeat(100),
        bankCode: '**********',
        agencyNumber: '**********',
        agencyDigit: '12345',
        accountNumber: '**********',
        accountDigit: '12345',
        accountHolderName: 'a'.repeat(100),
        accountHolderDocument: '**********1234',
        pixKey: 'a'.repeat(100),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
