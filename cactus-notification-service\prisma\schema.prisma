// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

// Database local para notificações
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["notifications", "public"]
}

// Database remota do backoffice para consulta de contratos
datasource backoffice {
  provider = "postgresql"
  url      = env("BACKOFFICE_DATABASE_URL")
  schemas  = ["core"]
}

// Modelos para database local de notificações
model Notification {
  id          String            @id @default(uuid())
  userId      String            @map("user_id")
  type        NotificationType
  title       String
  message     String
  data        Json?             // Dados adicionais da notificação
  isRead      Boolean           @default(false) @map("is_read")
  readAt      DateTime?         @map("read_at")
  createdAt   DateTime          @default(now()) @map("created_at")
  updatedAt   DateTime          @updatedAt @map("updated_at")
  
  @@map("notifications")
  @@schema("notifications")
}

model UserNotificationSettings {
  id                    String   @id @default(uuid())
  userId                String   @unique @map("user_id")
  contractExpiration    Boolean  @default(true) @map("contract_expiration")
  documentExpiration    Boolean  @default(true) @map("document_expiration")
  emailNotifications    Boolean  @default(true) @map("email_notifications")
  pushNotifications     Boolean  @default(true) @map("push_notifications")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")
  
  @@map("user_notification_settings")
  @@schema("notifications")
}

// Modelos para consulta na database do backoffice (read-only)
model BackofficeContract {
  id             String                      @id
  entityType     BackofficeEntityType
  entityUuid     String                      @map("entityUuid")
  contractType   BackofficeContractType
  currentVersion Int                         @default(1) @map("currentVersion")
  status         BackofficeContractStatus    @default(PENDING)
  createdAt      DateTime                    @default(now()) @map("created_at")
  updatedAt      DateTime                    @updatedAt @map("updated_at")
  createdBy      String                      @map("created_by")
  updatedBy      String                      @map("updated_by")
  versions       BackofficeContractVersion[]

  @@map("contracts")
  @@schema("core")
}

model BackofficeContractVersion {
  id             String                @id
  versionId      Int                   @map("versionId")
  uploadedAt     DateTime              @default(now()) @map("uploadedAt")
  uploadedBy     String                @map("uploadedBy")
  filePath       String                @map("filePath")
  signed         Boolean               @default(false)
  validatedBy    String?               @map("validatedBy")
  validatedAt    DateTime?             @map("validatedAt")
  startDate      DateTime?             @map("startDate")
  expirationDate DateTime?             @map("expirationDate")
  observations   String?
  contractId     String                @map("contractId")
  createdAt      DateTime              @default(now()) @map("created_at")
  contract       BackofficeContract    @relation(fields: [contractId], references: [id])

  @@unique([contractId, versionId])
  @@map("contract_versions")
  @@schema("core")
}

model BackofficeCustomer {
  id          Int                         @id @default(autoincrement())
  uuid        String                      @unique
  razaoSocial String                      @unique @map("razaoSocial")
  cnpj        String                      @unique
  email       String
  status      BackofficeCustomerStatus    @default(ACTIVE)
  createdAt   DateTime                    @default(now()) @map("created_at")
  updatedAt   DateTime                    @updatedAt @map("updated_at")

  @@map("customers")
  @@schema("core")
}

// Enums para notificações locais
enum NotificationType {
  CONTRACT_EXPIRING
  CONTRACT_EXPIRED
  DOCUMENT_EXPIRING
  DOCUMENT_EXPIRED
  SYSTEM_ALERT
  
  @@schema("notifications")
}

// Enums do backoffice (read-only)
enum BackofficeEntityType {
  CLIENT
  COLLABORATE
  SUPPLIER

  @@schema("core")
}

enum BackofficeContractType {
  CERT_GAME
  CERT_RNG
  CERT_RGS
  CERT_PLATFORM
  CERT_INTEGRATION
  CERT_KYC
  CERT_PAYMENT
  CERT_SPORTSBOOK

  @@schema("core")
}

enum BackofficeContractStatus {
  PENDING
  APPROVED
  REJECTED

  @@schema("core")
}

enum BackofficeCustomerStatus {
  ACTIVE
  INACTIVE
  PENDING

  @@schema("core")
}
